import type { RouteRecordRaw } from 'vue-router'

export declare interface Fn<T = any, R = T> {
  (...arg: T[]): R
}

export declare interface AsyncFn<T = any, R = T> {
  (...arg: T[]): Promise<R>
}

declare module 'element-plus' {
  interface UploadFile {
    // 文件对象名，通过objectName从minio中获取文件url
    objectName?: string
  }
}

export interface AddressInfo {
  latitude?: number | string
  longitude?: number | string
  address?: string
}
