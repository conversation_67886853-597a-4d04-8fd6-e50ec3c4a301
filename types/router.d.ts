import type { i18n } from '@/lang'
import 'vue-router'

declare module 'vue-router' {
  // https://router.vuejs.org/zh/guide/advanced/meta.html#typescript
  // 可以通过扩展 RouteMeta 接口来输入 meta 字段
  interface RouteMeta {
    // 菜单名称
    title?: Parameters<typeof i18n.global.t>
    /**
     * 菜单权限
     */
    permission?: number | string
    // 是否是只有一个子菜单
    singleChild?: boolean
    // 侧边栏icon
    icon?: string
    // 是否隐藏
    hidden?: boolean
    // 高亮菜单：为详情页设置高亮菜单
    activeMenu?: string
  }
}
