{"name": "vue-template", "type": "module", "version": "0.0.0", "private": true, "packageManager": "pnpm@10.10.0", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint .", "lint:fix": "pnpm run lint --fix", "test": "vitest --run --coverage --isolate", "style": "stylelint \"src/**/*.(vue|scss|css)\" --fix", "svgo": "svgo -r -f ./src/icons --config ./src/icons/config.js"}, "dependencies": {"@element-plus/icons-vue": "^2.3.2", "@vueuse/core": "^13.9.0", "animate.css": "^4.1.1", "axios": "1.11.0", "dayjs": "^1.11.18", "default-passive-events": "^4.0.0", "element-plus": "^2.11.3", "leaflet": "1.9.4", "minio-js": "^1.0.7", "mitt": "^3.0.1", "normalize.css": "^8.0.1", "nprogress": "^0.2.0", "pinia": "^3.0.3", "qs": "^6.14.0", "vue": "^3.5.21", "vue-router": "4.5.1"}, "devDependencies": {"@antfu/eslint-config": "^5.2.2", "@iconify-json/ep": "^1.2.3", "@iconify/utils": "^3.0.1", "@types/leaflet": "^1.9.20", "@types/node": "^24.5.2", "@types/nprogress": "^0.2.3", "@unocss/preset-rem-to-px": "^66.5.1", "@vitejs/plugin-vue": "^6.0.1", "@vitest/coverage-v8": "^3.2.4", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "eslint": "^9.35.0", "eslint-plugin-format": "^1.0.1", "postcss": "^8.5.6", "postcss-px-to-viewport": "^1.1.1", "rollup-plugin-visualizer": "^6.0.3", "sass": "^1.89.2", "stylelint": "^16.21.1", "stylelint-config-html": "^1.1.0", "stylelint-config-recess-order": "^7.1.0", "stylelint-config-recommended-scss": "^15.0.1", "stylelint-config-recommended-vue": "^1.6.1", "stylelint-config-standard": "^38.0.0", "stylelint-config-standard-scss": "^15.0.1", "svgo": "^4.0.0", "typescript": "^5.9.2", "unocss": "^66.5.1", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.8.0", "vite": "7.1.5", "vite-plugin-restart": "^0.4.2", "vite-plugin-vue-devtools": "^8.0.2", "vitest": "3.2.4", "vue-tsc": "^3.0.1"}}