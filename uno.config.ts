import fs from 'node:fs'
import path from 'node:path'
import { FileSystemIconLoader } from '@iconify/utils/lib/loader/node-loaders'
import presetRemToPx from '@unocss/preset-rem-to-px'
import { presetAttributify, presetIcons, presetWind3, transformerDirectives, transformerVariantGroup } from 'unocss'
import Unocss from 'unocss/vite'

// 本地 SVG 图标存放目录
const iconsDir = './src/icons'

// 递归读取目录并返回所有目录信息
function getIconDirectories(dir: string, basePath: string = dir): Array<{ path: string, name: string, files: string[] }> {
  const result: Array<{ path: string, name: string, files: string[] }> = []

  try {
    const items = fs.readdirSync(dir)
    const svgFiles = items.filter((item) => {
      const itemPath = path.join(dir, item)
      const stats = fs.statSync(itemPath)
      return stats.isFile() && item.endsWith('.svg')
    })

    // 添加当前目录信息
    const dirName = dir === basePath ? 'icon' : path.basename(dir)
    result.push({
      path: dir,
      name: dirName,
      files: svgFiles,
    })

    // 递归处理子目录
    const subDirs = items.filter((item) => {
      const itemPath = path.join(dir, item)
      const stats = fs.statSync(itemPath)
      return stats.isDirectory()
    })

    for (const subDir of subDirs) {
      const subDirPath = path.join(dir, subDir)
      result.push(...getIconDirectories(subDirPath, basePath))
    }
  }
  catch (error) {
    console.error(`无法读取图标目录 ${dir}:`, error)
  }

  return result
}

// 生成 collections 配置
function generateCollections(directories: Array<{ path: string, name: string, files: string[] }>) {
  const collections: Record<string, any> = {}

  for (const { path: dirPath, name } of directories) {
    collections[name] = FileSystemIconLoader(dirPath, (svg) => {
      // 如果 SVG 文件未定义 `fill` 属性，则默认填充 `currentColor`
      // 这样图标颜色会继承文本颜色，方便在不同场景下适配
      return svg.includes('fill="')
        ? svg
        : svg.replace(/^<svg /, '<svg fill="currentColor" ')
    })
  }

  return collections
}

// 生成 safelist
function generateSafeList(directories: Array<{ path: string, name: string, files: string[] }>) {
  const safelist: string[] = []

  for (const { name, files } of directories) {
    for (const file of files) {
      const iconName = file.replace('.svg', '')
      safelist.push(`i-${name}-${iconName}`)
    }
  }

  return safelist
}

export function unocssPlugin() {
  // 只执行一次目录读取，避免重复执行
  const directories = getIconDirectories(iconsDir)

  return Unocss({
    presets: [
      presetAttributify({
        /* preset options */
      }),
      presetWind3(),
      presetIcons({
        extraProperties: {
          'width': '1em',
          'height': '1em',
          'display': 'inline-block',
          'flex-shrink': '0',
        },
        collections: generateCollections(directories),
      }),
      presetRemToPx({ baseFontSize: 4 }),
    ],
    safelist: generateSafeList(directories),
    transformers: [transformerVariantGroup(), transformerDirectives()],
    theme: {
      colors: {
        bg: 'var(--el-bg-color)',
        primary: 'var(--el-color-primary)',
        success: 'var(--el-color-success)',
        warning: 'var(--el-color-warning)',
        danger: 'var(--el-color-danger)',

        text: {
          primary: 'var(--el-text-color-primary)',
          regular: 'var(--el-text-color-regular)',
          secondary: 'var(--el-text-color-secondary)',
          placeholder: 'var(--el-text-color-placeholder)',
          disabled: 'var(--el-text-color-disabled)',
          DEFAULT: 'var(--el-text-color-primary)',
        },

        border: {
          light: 'var(--el-border-color-light)',
          lighter: 'var(--el-border-color-lighter)',
          DEFAULT: 'var(--el-border-color)',
        },
      },

      fontSize: {
        'extra-small': 'var(--el-font-size-extra-small)',
        'small': 'var(--el-font-size-small)',
        'base': 'var(--el-font-size-base)',
        'medium': 'var(--el-font-size-medium)',
        'large': 'var(--el-font-size-large)',
        'extra-large': 'var(--el-font-size-extra-large)',
      },

      lineHeight: {
        'extra-small': 'var(--el-font-line-height-extra-small)',
        'small': 'var(--el-font-line-height-small)',
        'base': 'var(--el-font-line-height-base)',
        'medium': 'var(--el-font-line-height-medium)',
        'large': 'var(--el-font-line-height-large)',
        'extra-large': 'var(--el-font-line-height-extra-large)',
      },

      borderRadius: {
        small: 'var(--el-border-radius-small)',
        base: 'var(--el-border-radius-base)',
        round: 'var(--el-border-radius-round)',
      },
    },
    extendTheme: (theme) => {
      // 查看 theme 结构，便于调试
      console.log(theme)
    },

  })
}
