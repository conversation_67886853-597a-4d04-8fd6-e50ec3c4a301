import type { LoginData } from '@/api/system/auth'
import type { UserInfo } from '@/api/system/user'
import { defineStore } from 'pinia'
import AuthAPI from '@/api/system/auth'
import { UserAPI } from '@/api/system/user'
import router from '@/router'
import { resetAllStores, store } from '@/store/index'
import { usePermissionStoreHook } from '@/store/permisson.ts'
import { TOKEN_KEY } from '@/utils/request'

export const useUserStore = defineStore('user', {
  state: () => ({
    user: { roles: [], perms: [] } as UserInfo,
    perMap: {} as Record<string, boolean>,
  }),
  actions: {
    login(loginData: LoginData) {
      return new Promise<void>((resolve, reject) => {
        AuthAPI.login(loginData)
          .then((data) => {
            const { tokenType, accessToken } = data
            localStorage.setItem(TOKEN_KEY, `${tokenType} ${accessToken}`) // Bearer eyJhbGciOiJIUzI1NiJ9.xxx.xxx
            resolve()
          })
          .catch((error) => {
            reject(error)
          })
      })
    },

    getUserInfo() {
      return new Promise<UserInfo>((resolve, reject) => {
        UserAPI.getInfo()
          .then((data) => {
            if (!data) {
              reject(new Error('Verification failed, please Login again.'))
              return
            }

            if (!data.roles || data.roles.length <= 0) {
              reject(new Error('getUserInfo: roles must be a non-null array!'))
              return
            }

            this.user = data
            this.perMap = {}
            for (const item of [...data.perms, ...data.menus]) {
              this.perMap[item] = true
            }

            resolve(data)
          })
          .catch((error) => {
            reject(error)
          })
      })
    },

    logout() {
      return new Promise<void>((resolve, reject) => {
        AuthAPI.logout()
          .then(() => {
            this.clearAppState()
            resolve()
          })
          .catch((error) => {
            reject(error)
          })
      })
    },

    /**
     * 清除系统状态并重置到登录页
     */
    clearAppState(query = {}) {
      // 清除token
      localStorage.removeItem(TOKEN_KEY)
      router.push({ path: '/login', query }).then(() => {
        // 清除路由
        const permissionStore = usePermissionStoreHook()
        permissionStore.clearRoutes()
        // 重置所有 pinia
        resetAllStores()
      })
    },

    resetToken() {
      return new Promise<void>((resolve) => {
        localStorage.removeItem(TOKEN_KEY)
        resolve()
      })
    },
  },
})

// 非setup
export function useUserStoreHook() {
  return useUserStore(store)
}
