// 自定义插件：将纯色图标的 fill 改为 currentColor
const convertMonochromeToCurrentColor = {
  name: 'convertMonochromeToCurrentColor',
  fn: () => {
    return {
      element: {
        enter: (node, parentNode) => {
          // 只处理根 svg 元素，收集所有的 fill 颜色
          if (node.name === 'svg' && !parentNode.name) {
            const fillColors = new Set()
            const strokeColors = new Set()

            // 递归遍历所有子元素，收集颜色
            const collectColors = (element) => {
              if (element.attributes) {
                // 收集 fill 颜色
                if (element.attributes.fill
                  && element.attributes.fill !== 'none'
                  && element.attributes.fill !== 'transparent'
                  && element.attributes.fill !== 'currentColor') {
                  fillColors.add(element.attributes.fill.toLowerCase())
                }

                // 收集 stroke 颜色
                if (element.attributes.stroke
                  && element.attributes.stroke !== 'none'
                  && element.attributes.stroke !== 'transparent'
                  && element.attributes.stroke !== 'currentColor') {
                  strokeColors.add(element.attributes.stroke.toLowerCase())
                }
              }

              // 递归处理子元素
              if (element.children) {
                element.children.forEach((child) => {
                  if (child.type === 'element') {
                    collectColors(child)
                  }
                })
              }
            }

            // 收集所有颜色
            collectColors(node)

            // 判断是否为纯色图标（只有一种 fill 颜色，可能没有 stroke 或只有一种 stroke 颜色）
            const isMonochrome = fillColors.size <= 1 && strokeColors.size <= 1

            // 如果是纯色图标，将所有颜色替换为 currentColor
            if (isMonochrome && (fillColors.size > 0 || strokeColors.size > 0)) {
              const replaceColors = (element) => {
                if (element.attributes) {
                  // 替换 fill
                  if (element.attributes.fill
                    && element.attributes.fill !== 'none'
                    && element.attributes.fill !== 'transparent') {
                    element.attributes.fill = 'currentColor'
                  }

                  // 替换 stroke
                  if (element.attributes.stroke
                    && element.attributes.stroke !== 'none'
                    && element.attributes.stroke !== 'transparent') {
                    element.attributes.stroke = 'currentColor'
                  }
                }

                // 递归处理子元素
                if (element.children) {
                  element.children.forEach((child) => {
                    if (child.type === 'element') {
                      replaceColors(child)
                    }
                  })
                }
              }

              // 执行替换
              replaceColors(node)
            }
          }
        },
      },
    }
  },
}

export default {
  multipass: true,
  floatPrecision: 4,
  plugins: [
    {
      name: 'preset-default',
    },
    {
      name: 'removeAttributesBySelector',
      params: {
        selector: 'svg',
        attributes: ['xml:space', 'id', 'baseProfile'],
      },
    },
    {
      name: 'removeAttrs',
      params: {
        attrs: ['data-*', 'data.*'],
      },
    },
    {
      name: 'convertStyleToAttrs',
      params: {
        keepImportant: true,
      },
    },
    {
      name: 'sortAttrs',
    },
    // 使用自定义插件替换纯色图标的颜色
    convertMonochromeToCurrentColor,
  ],
}
