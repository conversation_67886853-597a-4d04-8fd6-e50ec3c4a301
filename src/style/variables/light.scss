.light {
  --el-text-color-disabled: var(--Light-Text-Disabled);
  --el-icon-color-disabled: var(--Light-Text-Disabled);
  --el-icon-color-placeholder: var(--Light-Text-Placeholder);
  --el-icon-color-secondary: var(--Light-Text-Secondary);
  --el-icon-color-regular: var(--Light-Text-Regular);
  --el-icon-color-primary: var(--Light-Text-Primary);
  --el-icon-color-write: var(--White);
  --el-text-color-placeholder: var(--Light-Text-Placeholder);
  --el-text-color-secondary: var(--Light-Text-Secondary);
  --el-text-color-regular: var(--Light-Text-Regular);
  --el-text-color-primary: var(--Light-Text-Primary);
  --el-text-color-write: var(--White);
  --Fill-Blank: var(--Light-Fill-Blank);
  --el-fill-color-extra-light: var(--Light-Fill-ExtraLight);
  --el-fill-color-lighter: var(--Light-Fill-Lighter);
  --el-fill-color-light: var(--Light-Fill-Light);
  --el-fill-color: var(--Light-Fill-Base);
  --el-fill-color-dark: var(--Light-Fill-Dark);
  --el-fill-color-darker: var(--Light-Fill-Darker);
  --el-bg-color-page: var(--Light-Background-Page);
  --el-bg-color: var(--Light-Background-Base);
  --el-bg-color-overlay: var(--Light-Background-Overlay);
  --Bg-Opposite: var(--Light-Background-Transparent);
  --el-border-color-darker: var(--Light-Border-Darker);
  --el-border-color-dark: var(--Light-Border-Dark);
  --el-border-color: var(--Light-Border-Base);
  --el-border-color-light: var(--Light-Border-Light);
  --el-border-color-lighter: var(--Light-Border-Lighter);
  --el-border-color-extra-light: var(--Light-Border-ExtraLight);
  --Border-0: var(--0);
  --el-border-width: var(--1);
  --Border-2: var(--2);
  --Border-fff: var(--Light-Fill-Blank);
  --el-color-primary-dark-2: var(--Light-Primary-Primary-Dark-2);
  --el-color-success-dark-2: var(--Light-Success-Success-Dark-2);
  --el-color-success: var(--Light-Success-Success);
  --el-color-success-3: var(--Light-Success-Success-3);
  --el-color-success-5: var(--Light-Success-Success-5);
  --el-color-success-7: var(--Light-Success-Success-7);
  --el-color-success-8: var(--Light-Success-Success-8);
  --el-color-success-9: var(--Light-Success-Success-9);
  --el-color-primary: var(--Light-Primary-Primary);
  --el-color-primary-3: var(--Light-Primary-Primary-3);
  --el-color-primary-5: var(--Light-Primary-Primary-5);
  --el-color-primary-7: var(--light-primary-7);
  --el-color-primary-8: var(--light-primary-8);
  --el-color-primary-9: var(--Light-Primary-Primary-9);
  --el-color-warning-dark-2: var(--Light-Warning-Warning-Dark-2);
  --el-color-warning: var(--Light-Warning-Warning);
  --el-color-error-dark-2: var(--Light-Error-Error-Dark-2);
  --el-color-error: var(--Light-Error-Error);
  --el-color-error-3: var(--Light-Error-Error-3);
  --el-color-error-5: var(--Light-Error-Error-5);
  --el-color-error-7: var(--Light-Error-Error-7);
  --el-color-error-8: var(--Light-Error-Error-8);
  --el-color-error-9: var(--Light-Error-Error-9);
  --el-color-warning-3: var(--Light-Warning-Warning-3);
  --el-color-warning-5: var(--Light-Warning-Warning-5);
  --el-color-warning-7: var(--Light-Warning-Warning-7);
  --el-color-warning-8: var(--Light-Warning-Warning-8);
  --el-color-warning-9: var(--Light-Warning-Warning-9);
  --el-color-info-dark-2: var(--Light-Info-Info-Dark-2);
  --el-color-info: var(--Light-Info-Info);
  --el-color-info-3: var(--Light-Info-Info-3);
  --el-color-info-5: var(--Light-Info-Info-5);
  --el-color-info-7: var(--Light-Info-Info-7);
  --el-color-info-8: var(--Light-Info-Info-8);
  --el-color-info-9: var(--Light-Info-Info-9);
  --el-mask-color: var(--Light-Mask-Base);
  --el-mask-color-extra-light: var(--Light-Mask-ExtraLight);
  --el-overlay-color: var(--Light-Overlay-Base);
  --el-overlay-color-light: var(--Light-Overlay-Light);
  --el-overlay-color-lighter: var(--Light-Overlay-Lighter);
  --el-disabled-border-color: var(--Border-Light);
  --el-disabled-bg-color: var(--Fill-Light);
  --el-disabled-text-color: var(--Text-Disabled);
  --el-color-white: var(--White-100);
  --el-color-black: var(--Black-100);
  --el-border-radius-ciecle: var(--Full);
  --el-border-radius-round: var(--20);
  --el-border-radius-base: var(--4);
  --el-border-radius-small: var(--2);
  --el-border-radius-none: var(--0);
  --el-size-icon-8: var(--8);
  --el-size-icon-10: var(--10);
  --Size-Button-Small: var(--24);
  --Size-Input-Small: var(--24);
  --el-size-icon-12: var(--12);
  --el-size-icon-14: var(--14);
  --Size-Button-Default: var(--32);
  --Size-Input-Default: var(--32);
  --el-size-icon-16: var(--16);
  --el-size-icon-18: var(--18);
  --Size-Button-Large: var(--40);
  --Size-Input-Large: var(--40);
  --el-size-icon-20: var(--20);
  --el-size-icon-24: var(--24);
  --el-size-icon-28: var(--28);
  --el-size-icon-32: var(--32);
  --el-size-icon-48: var(--48);
  --el-size-icon-64: var(--64);
  --el-size-icon-104: var(--104);
  --el-size-icon-128: var(--128);
  --Space-0: var(--0);
  --Space-2: var(--2);
  --Space-4: var(--4);
  --Space-6: var(--6);
  --Space-Small-8: var(--8);
  --Space-Default-12: var(--12);
  --Space-Large-16: var(--16);
  --Space-20: var(--20);
  --Space-24: var(--24);
  --Space-28: var(--28);
  --Space-32: var(--32);
  --Space-36: var(--36);
  --Space-40: var(--40);
  --Shadow-4: var(--Black-4);
  --Shadow-8: var(--Black-8);
  --Shadow-12: var(--Black-12);
  --Shadow-16: var(--Black-16);
  --Shadow-Num--8: var(---8);
  --Shadow-Num-0: var(--0);
  --Shadow-Num-4: var(--4);
  --Shadow-Num-6: var(--6);
  --Shadow-Num-8: var(--8);
  --Shadow-Num-12: var(--12);
  --Shadow-Num-16: var(--16);
  --Shadow-Num-20: var(--20);
  --Shadow-Num-32: var(--32);
  --Shadow-Num-48: var(--48);
}
