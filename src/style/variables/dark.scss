/* Theme - Dark */
.dark {
  --el-text-color-disabled: var(--Dark-Text-Disabled);
  --el-icon-color-disabled: var(--Dark-Text-Disabled);
  --el-icon-color-placeholder: var(--Dark-Text-Placeholder);
  --el-icon-color-secondary: var(--Dark-Info-Info-7);
  --el-icon-color-regular: var(--Dark-Text-Regular);
  --el-icon-color-primary: var(--Dark-Text-Primary);
  --el-icon-color-write: var(--White);
  --el-text-color-placeholder: var(--Dark-Text-Placeholder);
  --el-text-color-secondary: var(--Dark-Text-Secondary);
  --el-text-color-regular: var(--Dark-Text-Regular);
  --el-text-color-primary: var(--Dark-Text-Primary);
  --el-text-color-write: var(--White);
  --Fill-Blank: var(--Dark-Fill-Blank);
  --el-fill-color-extra-light: var(--Dark-Fill-ExtraLight);
  --el-fill-color-lighter: var(--Dark-Fill-Lighter);
  --el-fill-color-light: var(--Dark-Fill-Light);
  --el-fill-color: var(--Dark-Fill-Base);
  --el-fill-color-dark: var(--Dark-Fill-Dark);
  --el-fill-color-darker: var(--Dark-Fill-Darker);
  --el-bg-color-page: var(--Dark-Background-Page);
  --el-bg-color: var(--Dark-Background-Base);
  --el-bg-color-overlay: var(--Dark-Background-Overlay);
  --Bg-Opposite: var(--Dark-Background-Transparent);
  --el-border-color-darker: var(--Dark-Border-Darker);
  --el-border-color-dark: var(--Dark-Border-Dark);
  --el-border-color: var(--Dark-Border-Base);
  --el-border-color-light: var(--Dark-Border-Light);
  --el-border-color-lighter: var(--Dark-Border-Lighter);
  --el-border-color-extra-light: var(--Dark-Border-ExtraLight);
  --Border-0: var(--0);
  --el-border-width: var(--1);
  --Border-2: var(--2);
  --Border-fff: var(--Dark-Fill-Base);
  --el-color-primary-dark-2: var(--Dark-Primary-Primary-Dark-2);
  --el-color-success-dark-2: var(--Dark-Success-Success-Dark-2);
  --el-color-success: var(--Dark-Success-Success);
  --el-color-success-3: var(--Dark-Success-Success-3);
  --el-color-success-5: var(--Dark-Success-Success-5);
  --el-color-success-7: var(--Dark-Success-Success-7);
  --el-color-success-8: var(--Dark-Success-Success-8);
  --el-color-success-9: var(--Dark-Success-Success-9);
  --el-color-primary: var(--Dark-Primary-Primary);
  --el-color-primary-3: var(--Dark-Primary-Primary-3);
  --el-color-primary-5: var(--Dark-Primary-Primary-5);
  --el-color-primary-7: var(--Dark-Primary-Primary-7);
  --el-color-primary-8: var(--Dark-Primary-Primary-8);
  --el-color-primary-9: var(--Dark-Primary-Primary-9);
  --el-color-warning-dark-2: var(--Dark-Warning-Warning-Dark-2);
  --el-color-warning: var(--Dark-Warning-Warning);
  --el-color-error-dark-2: var(--Dark-Error-Error-Dark-2);
  --el-color-error: var(--Dark-Error-Error);
  --el-color-error-3: var(--Dark-Error-Error-3);
  --el-color-error-5: var(--Dark-Error-Error-5);
  --el-color-error-7: var(--Dark-Error-Error-7);
  --el-color-error-8: var(--Dark-Error-Error-8);
  --el-color-error-9: var(--Dark-Error-Error-9);
  --el-color-warning-3: var(--Dark-Warning-Warning-3);
  --el-color-warning-5: var(--Dark-Warning-Warning-5);
  --el-color-warning-7: var(--Dark-Warning-Warning-7);
  --el-color-warning-8: var(--Dark-Warning-Warning-8);
  --el-color-warning-9: var(--Dark-Warning-Warning-9);
  --el-color-info-dark-2: var(--Dark-Info-Info-Dark-2);
  --el-color-info: var(--Dark-Info-Info);
  --el-color-info-3: var(--Dark-Info-Info-3);
  --el-color-info-5: var(--Dark-Info-Info-5);
  --el-color-info-7: var(--Dark-Info-Info-7);
  --el-color-info-8: var(--Dark-Info-Info-8);
  --el-color-info-9: var(--Dark-Info-Info-9);
  --el-mask-color: var(--Dark-Mask-Base);
  --el-mask-color-extra-light: var(--Dark-Mask-ExtraLight);
  --el-overlay-color: var(--Dark-Overlay-Base);
  --el-overlay-color-light: var(--Dark-Overlay-Light);
  --el-overlay-color-lighter: var(--Dark-Overlay-Lighter);
  --el-disabled-border-color: var(--Border-Light);
  --el-disabled-bg-color: var(--Fill-Light);
  --el-disabled-text-color: var(--Text-Disabled);
  --el-color-white: var(--White-100);
  --el-color-black: var(--Black-100);
  --el-border-radius-ciecle: var(--Full);
  --el-border-radius-round: var(--20);
  --el-border-radius-base: var(--4);
  --el-border-radius-small: var(--2);
  --el-border-radius-none: var(--0);
  --el-size-icon-8: var(--8);
  --el-size-icon-10: var(--10);
  --Size-Button-Small: var(--24);
  --Size-Input-Small: var(--24);
  --el-size-icon-12: var(--12);
  --el-size-icon-14: var(--14);
  --Size-Button-Default: var(--32);
  --Size-Input-Default: var(--32);
  --el-size-icon-16: var(--16);
  --el-size-icon-18: var(--18);
  --Size-Button-Large: var(--40);
  --Size-Input-Large: var(--40);
  --el-size-icon-20: var(--20);
  --el-size-icon-24: var(--24);
  --el-size-icon-28: var(--28);
  --el-size-icon-32: var(--32);
  --el-size-icon-48: var(--48);
  --el-size-icon-64: var(--64);
  --el-size-icon-104: var(--104);
  --el-size-icon-128: var(--128);
  --Space-0: var(--0);
  --Space-2: var(--2);
  --Space-4: var(--4);
  --Space-6: var(--6);
  --Space-Small-8: var(--8);
  --Space-Default-12: var(--12);
  --Space-Large-16: var(--16);
  --Space-20: var(--20);
  --Space-24: var(--24);
  --Space-28: var(--28);
  --Space-32: var(--32);
  --Space-36: var(--36);
  --Space-40: var(--40);
  --Shadow-4: var(--Black-12);
  --Shadow-8: var(--Black-25);
  --Shadow-12: var(--Black-36);
  --Shadow-16: var(--Black-45);
  --Shadow-Num--8: var(---8);
  --Shadow-Num-0: var(--0);
  --Shadow-Num-4: var(--4);
  --Shadow-Num-6: var(--6);
  --Shadow-Num-8: var(--8);
  --Shadow-Num-12: var(--12);
  --Shadow-Num-16: var(--16);
  --Shadow-Num-20: var(--20);
  --Shadow-Num-32: var(--32);
  --Shadow-Num-48: var(--48);
}
