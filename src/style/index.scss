@use 'reset';
@use 'bigData';
@use 'variable';
@use 'transition';
@use 'element';
@use 'variables/number';
@use 'variables/colorMode';
@use 'variables/light';
@use 'variables/dark';

:root {
  font-family: Inter, Avenir, Helvetica, Arial, sans-serif;
  font-size: var(--el-font-size-base);
  font-weight: 400;
  line-height: 1;
  color: var(--el-text-color-primary);
  color-scheme: light dark;
  text-rendering: optimizelegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-size-adjust: 100%;
  line-break: anywhere;
  // 支持关键字过渡，如： height: auto
  interpolate-size: allow-keywords;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}

body {
  min-width: 320px;
  min-height: 100vh;
  background-color: var(--el-bg-color-page);
}

#app {
}

::view-transition-old(root),
::view-transition-new(root) {
  animation: none;
  mix-blend-mode: normal;
}

.table-page {
  display: flex;
  flex-direction: column;
  gap: var(--gap);
  height: var(--app-main-h);

  .page-header {
    display: flex;
    align-items: center;
    padding: 24px;
    background-color: var(--el-bg-color);
    border-radius: var(--el-border-radius-base);

    .el-form-item {
      margin-bottom: 0;
    }
  }

  .page-main {
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    padding: var(--Space-Large-16) 24px;

    overflow: hidden;
    background: var(--Fill-Blank, #fff);
    border-radius: var(--el-border-radius-base);
    border: 1px solid var(--el-border-color-lighter);

    .header {
      display: flex;
      align-items: center;
      margin-bottom: var(--Space-Large-16);
    }
    .el-table {
      //min-height: 300px;
    }
  }
}

/* 主题切换动画 */
/* 进入dark模式和退出dark模式时，两个图像的位置顺序正好相反 */
::view-transition-old(root) {
  z-index: 1;
}

::view-transition-new(root) {
  z-index: 2147483646;
}

/* 根据自己选择器修改 */
html.dark::view-transition-old(root) {
  z-index: 2147483646;
}
html.dark::view-transition-new(root) {
  z-index: 1;
}
