<script setup lang="ts">
import type { FormRules } from 'element-plus'
import type { LocationQuery } from 'vue-router'
import type { LoginData } from '@/api/system/auth.ts'
import { CreditCard, Lock, User } from '@element-plus/icons-vue'
import { useCountdown } from '@vueuse/core'
import { ref } from 'vue'
import { useRoute } from 'vue-router'
import AuthAPI from '@/api/system/auth.ts'
import { SYSTEM_TITLE } from '@/constants'
import router from '@/router'
import { useUserStore } from '@/store/user.ts'
import ForgetPassword from './components/ForgetPassword.vue'

const iframeLoaded = ref(false)
function iframeLoad() {
  iframeLoaded.value = true
}

const loginData = ref<LoginData>({})
const rules: FormRules<typeof loginData.value> = {
  username: [{ required: true, trigger: 'blur', message: '请输入用户名' }],
  password: [{ required: true, trigger: 'blur', message: '请输入密码' }],
  captchaCode: [{ required: true, trigger: 'blur', message: '请输入验证码' }],
  phone: [{ pattern: /^1[3-9|]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur', required: true }],
  phoneCode: [{ required: true, trigger: 'change', message: '请输入验证码' }],
}

/**
 * 获取验证码
 */
const captchaBase64 = ref('')
function getCaptcha() {
  AuthAPI.getCaptcha().then((data) => {
    loginData.value.captchaKey = data.captchaKey
    captchaBase64.value = data.captchaBase64
  })
}

/**
 * 记住账号密码功能
 */
const rememberAccount = ref(false)
const REMEMBER_ACCOUNT_KEY = 'loginRememberAccount'
const SAVED_USERNAME_KEY = 'savedUsername'
const SAVED_PASSWORD_KEY = 'savedPassword'

// 保存账号密码到本地存储（加密保存）
function saveAccountToLocal() {
  if (rememberAccount.value) {
    localStorage.setItem(REMEMBER_ACCOUNT_KEY, 'true')
    // 对用户名和密码进行加密后保存
    const encryptedUsername = loginData.value.username || ''
    const encryptedPassword = loginData.value.password || ''
    localStorage.setItem(SAVED_USERNAME_KEY, encryptedUsername)
    localStorage.setItem(SAVED_PASSWORD_KEY, encryptedPassword)
  }
  else {
    // 如果取消记住账号，清除本地存储
    localStorage.removeItem(REMEMBER_ACCOUNT_KEY)
    localStorage.removeItem(SAVED_USERNAME_KEY)
    localStorage.removeItem(SAVED_PASSWORD_KEY)
  }
}

// 从本地存储恢复账号密码（解密读取）
function loadAccountFromLocal() {
  const isRemembered = localStorage.getItem(REMEMBER_ACCOUNT_KEY) === 'true'
  if (isRemembered) {
    rememberAccount.value = true
    const encryptedUsername = localStorage.getItem(SAVED_USERNAME_KEY) || ''
    const encryptedPassword = localStorage.getItem(SAVED_PASSWORD_KEY) || ''

    // 解密用户名和密码
    const decryptedUsername = encryptedUsername
    const decryptedPassword = encryptedPassword

    if (decryptedUsername) {
      loginData.value.username = decryptedUsername
    }

    if (decryptedPassword) {
      loginData.value.password = decryptedPassword
    }
  }
}

loadAccountFromLocal()

const userStore = useUserStore()
const formRef = useTemplateRef('formRef')
const loading = ref(false)
const authType = ref('0')
const showCode = ref(false)
function login() {
  formRef.value?.validate().then(() => {
    loading.value = true
    loginData.value.authType = authType.value
    userStore
      .login(loginData.value)
      .then(() => {
        // 登录成功后保存账号密码（如果勾选了记住账号）
        saveAccountToLocal()
        const { path, queryParams } = parseRedirect()
        router.push({ path, query: queryParams })
      })
      .catch((error) => {
        showCode.value = error.data
        getCaptcha()
      })
      .finally(() => {
        loading.value = false
      })
  })
}

function changeAuthType(type: string) {
  authType.value = type
}

/**
 * 倒计时
 */
const countdown = 60
const { remaining, start } = useCountdown(countdown, {
  onComplete() {
    isCounting.value = false
  },
  onTick() {},
})

/**
 *  获取手机验证码
 */
const isCounting = ref(false)
const loadingPhoneCode = ref(false)
function getPhoneCode() {
  formRef.value.validateField('phone').then(() => {
    loadingPhoneCode.value = true
    AuthAPI.setPhoneCode({ phone: loginData.value.phone, type: 'login' }).then(() => {
      isCounting.value = true
      start()
    }).finally(() => {
      loadingPhoneCode.value = false
    })
  })
}

/**
 * 解析 redirect 字符串 为 path 和  queryParams
 */
const route = useRoute()
function parseRedirect(): { path: string, queryParams: Record<string, string> } {
  const query: LocationQuery = route.query
  const redirect = (query.redirect as string) ?? '/'

  const url = new URL(redirect, window.location.origin)
  const path = url.pathname
  const queryParams: Record<string, string> = {}

  url.searchParams.forEach((value, key) => {
    queryParams[key] = value
  })

  return { path, queryParams }
}

/**
 * 忘记密码
 */
const dialogVisible = ref(false)
function handleForgetPassword() {
  dialogVisible.value = true
}
</script>

<template>
  <div class="login size-screen">
    <iframe src="https://my.spline.design/r4xbot-0t5YZnEhmrPMZQgVriaRrAyQ/" class="fixed inset-0 size-full" @load="iframeLoad" />
    <img v-if="!iframeLoaded" src="@/assets/login/bg.webp" class="fixed inset-0 size-full object-cover" draggable="false" alt="" />

    <main class="fixed inset-0 size-full pointer-events-none">
      <header class="flex items-center h-64 py-12 px-24 bg-white/5">
        <img src="@/assets/logo.webp" alt="" class="size-24 ml-20 pointer-events-auto" />
        <span class="ml-8 font-bold pointer-events-auto" text="$el-text-color-primary 24">{{ SYSTEM_TITLE }}</span>
        <Theme class="ml-auto pointer-events-auto" />
      </header>

      <!--      登录表单 -->
      <section class="pl-140 pt-216 inline-block">
        <header class="font-bold leading-44 pointer-events-auto" text="$el-text-color-primary 36">
          <div>登录到</div>
          <div>{{ SYSTEM_TITLE }}</div>
        </header>

        <el-form ref="formRef" class="w-400 pointer-events-auto mt-48" size="large" :rules="rules" :model="loginData" @submit.prevent="login">
          <template v-if="authType === '0'">
            <el-form-item prop="username">
              <el-input v-model="loginData.username" placeholder="请输入内容" :prefix-icon="User" />
            </el-form-item>
            <el-form-item prop="password">
              <el-input v-model="loginData.password" type="password" show-password placeholder="请输入密码" :prefix-icon="Lock" />
            </el-form-item>
            <el-form-item v-if="showCode" prop="captchaCode">
              <el-input v-model="loginData.captchaCode" auto-complete="off" size="large" placeholder="验证码" :prefix-icon="CreditCard">
                <template #suffix>
                  <el-image :src="captchaBase64" class="cursor-pointer" @click="getCaptcha" />
                </template>
              </el-input>
            </el-form-item>
            <el-form-item>
              <div class="flex justify-between w-full">
                <el-checkbox v-model="rememberAccount" label="记住账号" @change="saveAccountToLocal" />
                <div>
                  <el-button type="primary" link @click="handleForgetPassword">忘记密码 ？</el-button>
                </div>
              </div>
            </el-form-item>
          </template>
          <template v-if="authType === '1'">
            <el-form-item prop="phone">
              <el-input v-model="loginData.phone" placeholder="请输入手机号" :prefix-icon="User" />
            </el-form-item>

            <el-form-item prop="phoneCode">
              <el-input v-model="loginData.phoneCode" auto-complete="off" placeholder="请输入验证码" :prefix-icon="CreditCard">
                <template #suffix>
                  <span v-if="isCounting" class="select-none" text="primary 14">{{ remaining }}s</span>
                  <el-button v-else link :loading="loadingPhoneCode" class="underline" type="primary" @click="getPhoneCode">获取验证码</el-button>
                </template>
              </el-input>
            </el-form-item>
          </template>
          <el-form-item>
            <el-button native-type="submit" :loading="loading" class="!w-full mt-16" type="primary">登录</el-button>
          </el-form-item>

          <footer>
            <el-button v-if="authType === '0' " link type="primary" @click="changeAuthType('1')">使用验证码登录</el-button>
            <el-button v-if="authType === '1' " link type="primary" @click="changeAuthType('0')">使用账号登录</el-button>
          </footer>
        </el-form>
      </section>

      <footer class="absolute bottom-64 left-140 pointer-events-auto" text="14 black/40">Copyright @ 2025-2026 VANKEY Design</footer>
      <ForgetPassword v-model="dialogVisible" />
    </main>
  </div>
</template>

<style scoped lang="scss">
:deep(.el-form) {
  .el-form-item {
    margin-bottom: 24px;
  }

  .el-input__wrapper {
    background-color: rgba(255, 255, 255, 0.7);
  }
}
</style>
