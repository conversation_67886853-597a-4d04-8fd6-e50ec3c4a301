<script setup lang="ts">
import type { FormRules } from 'element-plus'
import type { ForgetPassword } from '@/api/system/auth.ts'
import { useCountdown } from '@vueuse/core'
import AuthAPI from '@/api/system/auth.ts'

const dialogVisible = defineModel<boolean>()

const formData = ref<ForgetPassword>({})

const rules: FormRules<typeof formData.value> = {
  phone: [{ pattern: /^1[3-9|]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur', required: true }],
  phoneCode: [{ required: true, trigger: 'change', message: '请输入验证码' }],
  password: [
    { required: true, trigger: 'blur', message: '请输入新密码' }, // 密码长度不能小于6位
    { min: 6, message: '密码长度不能小于6位', trigger: 'blur' },
    // 密码强度验证：密码必须包含大写字母、小写字母、数字、特殊字符中至少两种组合
    {
      pattern: /^(?=.*[a-z])(?=.*[A-Z])|(?=.*[a-z])(?=.*\d)|(?=.*[a-z])(?=.*[@$!%*?&])|(?=.*[A-Z])(?=.*\d)|(?=.*[A-Z])(?=.*[@$!%*?&])|(?=.*\d)(?=.*[@$!%*?&]).{6,}$/,
      message: '密码必须包含大写字母、小写字母、数字、特殊字符中至少两种组合，且长度不少于6位',
      trigger: 'blur',

    },
  ],
  confirmPassword: [
    { required: true, trigger: 'blur', message: '请输入确认新密码' },
    // 确保两次输入的密码一致
    {
      validator: (rule, value) => {
        if (value !== formData.value.password) {
          return new Error('两次输入的密码不一致')
        }

        return true
      },
      trigger: 'blur',
    },

  ],
}

const formRef = useTemplateRef('formRef')

function handleDialogOpen() {
  step.value = 1
  formData.value = {}
}

type Step = 1 | 2
const step = ref<Step>(1)
const loading = ref(false)
function next() {
  formRef.value.validate().then(() => {
    const { phone, phoneCode } = formData.value
    loading.value = true
    AuthAPI.checkPhoneCode({ phone, phoneCode, type: 'forget-password' })
      .then((res) => {
        step.value = 2
        stop()
      })
      .finally(() => {
        loading.value = false
      })
  })
}

/**
 * 倒计时
 */
const countdown = 60
const { remaining, start, stop } = useCountdown(countdown, {
  onComplete() {
    isCounting.value = false
  },
  onTick() {},
})

/**
 *  获取手机验证码
 */
const isCounting = ref(false)
const loadingPhoneCode = ref(false)
function getPhoneCode() {
  formRef.value.validateField('phone').then(() => {
    loadingPhoneCode.value = true
    AuthAPI.setPhoneCode({ phone: formData.value.phone, type: 'forget-password' })
      .then(() => {
        ElMessage.success('验证码发送成功，请注意查收')
        isCounting.value = true
        start()
      })
      .finally(() => {
        loadingPhoneCode.value = false
      })
  })
}

function submit() {
  formRef.value.validate().then(() => {
    AuthAPI.resetCaptcha(formData.value).then((res) => {
      ElMessage.success('重置成功')
      dialogVisible.value = false
    })
  })
}
</script>

<template>
  <el-dialog v-model="dialogVisible" destroy-on-close append-to-body align-center title="忘记密码" width="30vw" @open="handleDialogOpen">
    <el-form ref="formRef" :rules="rules" :model="formData" label-width="4.5vw" p="x-30" class="mt-40" size="large">
      <template v-if="step === 1">
        <el-form-item label="手机号：" prop="phone">
          <el-input-number v-model="formData.phone" :controls="false" class="mb-5 !w-full" disabled-scientific align="left" placeholder="请输入" @keyup.enter="next" />
        </el-form-item>
        <el-form-item label="验证码：" prop="phoneCode">
          <div class="flex items-center justify-between w-full gap-20">
            <el-input v-model.trim="formData.phoneCode" placeholder="请输入验证码" @keyup.enter="next" />
            <el-button v-if="isCounting" class="w-150" type="primary">{{ remaining }}s</el-button>
            <el-button v-else :loading="loadingPhoneCode" class="!w-150" type="primary" @click="getPhoneCode">获取验证码</el-button>
          </div>
        </el-form-item>
      </template>
      <template v-else-if="step === 2">
        <el-form-item label="新密码" label-width="4.1vw" prop="password">
          <el-input v-model="formData.password" placeholder="密码" type="password" show-password @keyup.enter="submit" />
        </el-form-item>
        <el-form-item label="确认密码" label-width="4.1vw" prop="confirmPassword">
          <el-input v-model="formData.confirmPassword" placeholder="确认密码" type="password" show-password @keyup.enter="submit" />
        </el-form-item>
      </template>
    </el-form>
    <template #footer>
      <el-button bg text @click="dialogVisible = false">取消</el-button>
      <el-button v-if="step == 1" :loading="loading" type="primary" @click="next">下一步</el-button>
      <el-button v-else type="primary" @click="submit">提交</el-button>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">

</style>
