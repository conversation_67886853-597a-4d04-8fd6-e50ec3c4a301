<script setup lang="ts">
import type { Role } from '@/api/system/role'
import { Delete, Plus, QuestionFilled, Refresh } from '@element-plus/icons-vue'
import { RoleAPI } from '@/api/system/role'
import { getState } from '@/hooks'
import { auth } from '@/store/permisson'

// 移除多语言功能
const route = useRoute()

const state = getState<Role>()
const { queryParams, tableList, loading, dialogVisible, formData, title, total, rules, submitting } = toRefs(state)

// 表单验证规则
rules.value = {
  name: [{ required: true, message: '请输入角色名称', trigger: 'blur' }],
}

function handleQuery() {
  loading.value = true
  RoleAPI.pageList(queryParams.value)
    .then((res) => {
      tableList.value = res.records
      total.value = res.total
    })
    .finally(() => {
      loading.value = false
    })
}

handleQuery()

function search() {
  queryParams.value.pageNum = 1
  handleQuery()
}

const searchFormRef = useTemplateRef('searchFormRef')
function refreshSearch() {
  searchFormRef.value.resetFields()
  search()
}

function add() {
  formData.value = {}
  title.value = '新增'
  dialogVisible.value = true
}

function edit(row: typeof state.formData) {
  formData.value = { ...row }
  title.value = '编辑'
  dialogVisible.value = true
}

const formRef = useTemplateRef('formRef')
function submit() {
  formRef.value.validate().then(() => {
    submitting.value = true
    const fn = formData.value.id ? RoleAPI.update : RoleAPI.add
    fn(formData.value).then(() => {
      ElMessage.success('操作成功！')
      dialogVisible.value = false
      handleQuery()
    }).finally(() => {
      submitting.value = false
    })
  })
}

function handleDelete(row: typeof state.formData) {
  ElMessageBox.confirm(
    '确认删除已选中的数据项?',
    '警告',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    },
  ).then(() => {
    row.loading = true
    RoleAPI.delete([row.id])
      .then(() => {
        ElMessage.success('操作成功！')
        if (tableList.value.length === 1 && queryParams.value.pageNum > 1) {
          queryParams.value.pageNum -= 1
        }

        handleQuery()
      })
      .finally(() => {
        row.loading = false
      })
  })
}

// 启用禁用状态切换
function handleEnable(row: typeof state.formData): Promise<boolean> {
  row.loading2 = true
  return new Promise((resolve, reject) => {
    RoleAPI.enabled(row.id, !row.enabled)
      .then(() => {
        ElMessage.success('操作成功！')
        row.loading2 = false
        handleQuery()
        resolve(true)
      })
      .catch(() => {
        row.loading2 = false
        reject()
      })
  })
}

// 批量删除
const multipleSelection = ref<Role[]>([])
function handleSelectionChange(selection: any[]) {
  multipleSelection.value = selection
}

const batchDeleting = ref(false)
function handleBatchDelete() {
  if (multipleSelection.value.length === 0) {
    ElMessage.warning('请至少选择一项')
    return
  }

  ElMessageBox.confirm(
    '确认删除已选中的数据项?',
    '警告',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    },
  ).then(() => {
    batchDeleting.value = true
    const ids = multipleSelection.value.map(item => item.id)
    RoleAPI.delete(ids)
      .then(() => {
        ElMessage.success('操作成功！')
        if (tableList.value.length === ids.length && queryParams.value.pageNum > 1) {
          queryParams.value.pageNum -= 1
        }

        handleQuery()
      })
      .finally(() => {
        batchDeleting.value = false
      })
  })
}

// 权限配置相关
const authorityVisible = ref(false)
const treeData = ref([])
const parentChildLinked = ref(true)

const currentRoleId = ref('')
function handleConfigPermission(roleId: string) {
  currentRoleId.value = roleId
  authorityVisible.value = true
  loadRoleMenus(roleId)
}

// 加载菜单树
const authorityLoading = ref(false)
function loadMenuTree() {
  authorityLoading.value = true
  RoleAPI.roleOptions()
    .then((res) => {
      treeData.value = res
    })
    .finally(() => {
      authorityLoading.value = false
    })
}

loadMenuTree()

// 加载角色已有的菜单权限
const treeRef = useTemplateRef('treeRef')
function loadRoleMenus(roleId: string) {
  authorityLoading.value = true
  RoleAPI.getMenus(roleId).then((res) => {
    if (treeRef.value) {
      for (const menuId of res) {
        treeRef.value.setChecked(menuId, true, false)
      }
    }
  }).finally(() => {
    authorityLoading.value = false
  })
}

// 提交权限配置
function handleAuthoritySubmit() {
  if (!treeRef.value)
    return

  submitting.value = true
  const checkedKeys = treeRef.value.getCheckedKeys()
  const halfCheckedKeys = treeRef.value.getHalfCheckedKeys()
  const allKeys = [...checkedKeys, ...halfCheckedKeys]
  const menuIds = allKeys.map(Number).filter(id => !isNaN(id))

  RoleAPI.updateMenus(currentRoleId.value, menuIds)
    .then(() => {
      ElMessage.success('操作成功！')
      authorityVisible.value = false
    })
    .finally(() => {
      submitting.value = false
    })
}
</script>

<template>
  <div class="table-page">
    <header class="page-header">
      <el-form ref="searchFormRef" inline :model="queryParams" @submit.prevent>
        <el-form-item label="角色名称" prop="roleName">
          <el-input v-model="queryParams.roleName" placeholder="请输入" @change="search" />
        </el-form-item>
      </el-form>
      <el-button :icon="Refresh" @click="refreshSearch"> 重置 </el-button>
    </header>

    <main v-loading="loading" class="page-main">
      <header class="flex mb-24">
        <span class="font-bold" text="18 textPrimary"> {{ route.meta.title }} </span>
        <div class="ml-auto flex gap-10">
          <el-button :loading="batchDeleting" :icon="Delete" type="danger" :disabled="!multipleSelection.length" @click="handleBatchDelete"> 批量删除 </el-button>
          <el-button v-if="auth('system:role:add')" type="primary" :icon="Plus" @click="add"> 新增 </el-button>
        </div>
      </header>
      <el-table :data="tableList" stripe @selection-change="handleSelectionChange">
        <el-table-column type="selection" min-width="55" align="center" />
        <el-table-column prop="name" label="角色名称" min-width="120" align="center" show-overflow-tooltip />
        <el-table-column prop="modifyTime" label="更新时间" min-width="160" align="center" show-overflow-tooltip />
        <el-table-column prop="modifyBy" label="更新人" min-width="120" align="center" show-overflow-tooltip />
        <el-table-column label="状态" align="center" min-width="100">
          <template #default="{ row }">
            <el-switch
              v-model="row.enabled"
              :loading="row.loading2"
              :disabled="!auth('system:role:enable')"
              inline-prompt
              active-text="启用"
              inactive-text="禁用"
              :before-change="() => handleEnable(row)"
            />
          </template>
        </el-table-column>
        <el-table-column fixed="right" align="center" label="操作" min-width="200">
          <template #default="{ row }">
            <el-button v-if="auth('system:role:config')" type="primary" link @click="handleConfigPermission(row.id)"> 配置权限 </el-button>
            <el-button v-if="auth('system:role:edit')" link type="primary" @click="edit(row)"> 编辑 </el-button>
            <el-button v-if="auth('system:role:delete')" :loading="row.loading" type="danger" link @click="handleDelete(row)"> 删除 </el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        v-model:current-page="queryParams.pageNum"
        v-model:page-size="queryParams.pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        class="mt-20 ml-auto"
        @size-change="handleQuery"
        @current-change="handleQuery"
      />
    </main>

    <!-- 新增、编辑弹框 -->
    <el-dialog v-model="dialogVisible" destroy-on-close width="40vw" :title="title" append-to-body :close-on-click-modal="false">
      <el-form ref="formRef" :model="formData" :rules="rules" label-width="4.3vw" class="p-30" size="large" @submit.prevent>
        <el-form-item label="角色名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入角色名称" />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="submitting" type="primary" @click="submit"> 确认 </el-button>
          <el-button @click="dialogVisible = false"> 取消 </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 权限配置弹窗 -->
    <el-dialog v-model="authorityVisible" title="配置权限" width="40vw" destroy-on-close append-to-body :close-on-click-modal="false">
      <div class="p-20">
        <header class="flex justify-end items-center mb-20">
          <el-checkbox v-model="parentChildLinked"> 父子联动 </el-checkbox>
          <el-tooltip placement="bottom">
            <template #content>如果只需勾选菜单权限，不需要勾选子菜单或者按钮权限，请关闭父子联动</template>
            <el-icon class="cursor-pointer inline-block ml-4"> <QuestionFilled /> </el-icon>
          </el-tooltip>
        </header>

        <el-tree
          ref="treeRef"
          v-loading="authorityLoading"
          :data="treeData"
          show-checkbox
          node-key="value"
          :check-strictly="!parentChildLinked"
        />
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="authorityVisible = false"> 取消 </el-button>
          <el-button type="primary" :loading="submitting" @click="handleAuthoritySubmit"> 确认 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped></style>
