<script setup lang="ts">
import type { Monitor } from '@/api/monitor.ts'
import { Grid, List, Refresh, Setting, VideoCamera, View } from '@element-plus/icons-vue'
import { MonitorAPI } from '@/api/monitor.ts'
import { getState } from '@/hooks'

type ModeType = 'card' | 'list'
const modeType = ref<ModeType>('card')

function modeChange() {}

const state = getState<Monitor>()
const { loading, formData, queryParams, tableList, total } = toRefs(state)
queryParams.value.pageSize = 8
function handleQuery() {
  loading.value = true
  MonitorAPI.page(queryParams.value).then((res) => {
    tableList.value = res.records
    total.value = res.total
  }).finally(() => {
    loading.value = false
  })
}

handleQuery()

const installList = ref([
  { id: true, name: '已安装' },
  { id: false, name: '未安装' },
])

const deviceList = ref([
  { id: true, name: '在线' },
  { id: false, name: '离线' },
])

function search() {
  queryParams.value.pageNum = 1
  handleQuery()
}

const searchFormRef = useTemplateRef('searchFormRef')
function refreshSearch() {
  searchFormRef.value.resetFields()
  search()
}

const router = useRouter()
function play(row: Monitor) {
  router.push(`/list/monitor/${row.id}`)
}
</script>

<template>
  <div class="flex flex-col gap-$gap">
    <section class="bg-bg rounded-4" b="1 solid border-lighter">
      <header class="flex justify-between leading-extra-large" p="x-24 y-16" b="b-1 solid border">
        <aside class="flex items-center">
          <span class="font-bold" text="extra-large">搜索列表</span>
          <div class="w-1 h-12 bg-border-lighter mx-12" />
          <span text="14 text-secondary">这是描述文字</span>
          <el-tag type="success" size="small" class="ml-12">在线设备 156</el-tag>
          <el-tag type="danger" size="small" class="ml-12">离线设备 12</el-tag>
          <el-tag type="warning" size="small" class="ml-12">异常设备 3</el-tag>
          <el-tag type="primary" size="small" class="ml-12">录制中 128</el-tag>
        </aside>
        <aside>
          <el-button>批量操作</el-button>
          <el-button type="primary">全部重启</el-button>
        </aside>
      </header>
      <div class="flex justify-between" p="x-24 y-16">
        <el-form ref="searchFormRef" :model="queryParams" class="flex gap-x-48 gap-y-16 flex-wrap">
          <el-form-item label="客户名称：" prop="customerName">
            <el-input v-model="queryParams.customerName" class="!w-300" placeholder="请输入关键词" clearable @change="search" />
          </el-form-item>

          <el-form-item label="安装状态：">
            <el-select v-model="queryParams.installed" clearable placeholder="不限" class="!w-200" @change="search">
              <el-option v-for="item in installList" :key="Number(item.id)" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="区域名称" prop="areaName">
            <el-input v-model="queryParams.areaName" clearable placeholder="不限" class="!w-200" @change="search" />
          </el-form-item>
          <el-form-item label="设备状态" prop="online">
            <el-select v-model="queryParams.online" clearable placeholder="请选择" class="!w-200" @change="search">
              <el-option v-for="(item) in deviceList" :key="Number(item.id)" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-form>
        <div class="flex items-center gap-12">
          <el-button :icon="Refresh" @click="refreshSearch">重置</el-button>
          <el-radio-group v-model="modeType" class="toggleMode" text-color="var(--el-color-primary)" @change="modeChange">
            <el-radio-button value="card"> <el-icon><Grid /></el-icon> </el-radio-button>
            <el-radio-button value="list"> <el-icon><List /></el-icon> </el-radio-button>
          </el-radio-group>
        </div>
      </div>
    </section>

    <main v-loading="loading" class="grow flex flex-col bg-bg rounded-4  px-32 py-16" b="1 solid border-lighter">
      <template v-if="tableList.length">
        <transition name="slide-bottom" mode="out-in">
          <section v-if="modeType === 'card' " class="gap-24" grid="~ cols-4">
            <div v-for="item in tableList" :key="item.id" b="1 solid border">
              <header class="w-full aspect-video relative">
                <Image :src="item.snapUrl" class="w-full h-full" :preview-src-list="[]" />
                <i class="i-base-play absolute left-1/2 top-1/2 -translate-1/2 cursor-pointer" text="white 50" @click="play(item)" />
                <footer class="h-30 flex justify-between items-center bg-black/50 absolute bottom-0" inset="x-0" p="y-4 x-10">
                  <div />
                  <div class="flex items-center gap-4">
                    <template v-if="item.online">
                      <i class="i-monitorStatus-online" text="12 success" />
                      <span text="small white">在线</span>
                    </template>
                    <template v-else>
                      <i class="i-monitorStatus-online" text="12 danger" />
                      <span text="small white">离线</span>
                    </template>
                  </div>
                </footer>
              </header>
              <section class="flex flex-col gap-4" p="x-20 y-12">
                <header class="flex items-center justify-between" text="medium">
                  <span>{{ item.channelName }}</span>
                  <el-tag type="info">{{ item.serialNum }}</el-tag>
                </header>
                <div class="leading-base" text="text-regular">
                  <span>通道号：</span>
                  <span>{{ item.channelId }}</span>
                </div>
                <div class="leading-base" text="text-regular">
                  <span>批次号：</span>
                  <span>{{ item.batchNum }}</span>
                </div>
                <div class="leading-base" text="text-regular">
                  <span>设备：</span>
                  <span>海康威视 DS-2CD</span>
                </div>
                <div class="leading-base flex items-center" text="text-regular">
                  <i class="i-temp-single" text="16" />
                  <span class="ml-6">信号强度：</span>
                  <span>95%</span>
                  <i class="i-temp-storage ml-20" text="16" />
                  <span class="ml-6">存储空间：</span>
                  <span>78%</span>
                </div>
                <footer class="flex justify-between">
                  <el-button :icon="View">预览</el-button>
                  <el-button :icon="VideoCamera">录制</el-button>
                  <el-button :icon="Refresh">重启</el-button>
                  <el-button :icon="Setting">设置</el-button>
                </footer>
              </section>
            </div>
          </section>

          <el-table v-else stripe :data="tableList">
            <el-table-column prop="serialNum" label="设备编号" align="center" min-width="120" show-overflow-tooltip />
            <el-table-column prop="channelId" label="通道号" align="center" min-width="120" show-overflow-tooltip />
            <el-table-column prop="deviceTotal" label="快照" align="center" min-width="120">
              <template #default="{ row }">
                <Image v-if="row.snapUrl" class="w-30 h-30 cursor-pointer" :src="row.snapUrl" />
              </template>
            </el-table-column>
            <el-table-column prop="channelName" label="通道名称" align="center" min-width="120" show-overflow-tooltip />
            <el-table-column prop="customerName" label="客户名称" align="center" min-width="120" show-overflow-tooltip />
            <el-table-column prop="areaName" label="区域名称" align="center" min-width="120" show-overflow-tooltip />
            <el-table-column prop="online" label="设备状态" align="center" min-width="120">
              <template #default="{ row }">
                <span :class="row.online ? 'text-success' : 'text-warming'">{{ row.online ? '在线' : '离线' }}</span>
              </template>
            </el-table-column>

            <el-table-column align="center" label="操作" min-width="160">
              <template #default="{ row }">
                <el-button size="small" type="primary" link>苗情记录</el-button>
                <el-button size="small" type="primary" link>预览</el-button>
                <el-button size="small" type="primary" link>查看录像</el-button>
                <el-button size="small" type="danger" link>删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </transition>
      </template>
      <el-empty v-else class="m-auto" />
      <el-pagination
        v-if="tableList.length"
        v-model:current-page="queryParams.pageNum"
        v-model:page-size="queryParams.pageSize"
        :total="total"
        :page-sizes="[8, 12, 16, 20]"
        layout="total, sizes, prev, pager, next, jumper"
        class="mt-20 ml-auto"
        @size-change="handleQuery"
        @current-change="handleQuery"
      />
    </main>
  </div>
</template>

<style scoped lang="scss">
:deep(.toggleMode) {
  .el-radio-button {
    --el-radio-button-checked-bg-color: transparent;
    .el-radio-button__inner {
      background-color: transparent;
    }
  }
}
</style>
