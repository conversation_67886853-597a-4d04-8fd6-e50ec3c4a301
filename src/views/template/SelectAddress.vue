<script setup lang="ts">
import type { AddressInfo } from '#/index'

const dialogVisible = ref(false)

const editable = ref(false)
function openDialog() {
  editable.value = true
  dialogVisible.value = true
}

function detail() {
  editable.value = false
  dialogVisible.value = true
}

const data = ref<AddressInfo>({})

function addressChange() {
  console.log(data.value)
}
</script>

<template>
  <div class="bg-bg" p="x-32 y-16">
    <el-button type="primary" @click="openDialog">选择地址</el-button>
    <el-button type="primary" @click="detail">地址详情</el-button>
    <AddressSelect v-model="dialogVisible" v-model:data="data" :editable="editable" @change="addressChange" />
    <div class="mt-20">
      <span>坐标信息：</span>
      <span>地址：{{ data.address || '--' }}</span>
      <span class="mx-26">纬度：{{ data.latitude || '--' }}</span>
      <span>经度：{{ data.longitude || '--' }}</span>
    </div>
  </div>
</template>

<style scoped lang="scss">

</style>
