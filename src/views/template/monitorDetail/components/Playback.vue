<script setup lang="ts">
import type { RecordItem } from '@/api/monitor.ts'
import dayjs from 'dayjs'
import { MonitorAPI } from '@/api/monitor.ts'
import { formatDuration } from '@/utils'

const { deviceId, channelId } = defineProps<{
  deviceId: string
  channelId: string
}>()

const tableList = ref<RecordItem[]>([])
const loading = ref(false)
function handleQuery() {
  loading.value = true
  MonitorAPI.deviceRecord(deviceId, channelId, '2024-01-01 00:00:00', '2026-01-02 00:00:00').then((res) => {
    tableList.value = res.recordList
  }).finally(() => {
    loading.value = false
  })
}

handleQuery()

/**
 * 播放录像
 */
const current = ref<RecordItem>(null)
function play(row: RecordItem) {
  console.log(row)
  current.value = row
}
</script>

<template>
  <div v-loading="loading" class="flex flex-col">
    <header class="flex items-center gap-5 h-22 mb-16">
      <i class="i-temp-monitor" text="16 primary" />
      <span>录像片段</span>
      <span>（{{ tableList.length }}个）</span>
    </header>
    <el-scrollbar height="calc(100vh - 16.8vw)">
      <div class="flex flex-col gap-10 leading-base">
        <div v-for="item in tableList" :key="item.filePath" :class="{ active: current === item }" class="rounded-4 cursor-pointer" p="x-16 y-12" b="1 solid border" @click="play(item)">
          <div class="flex justify-between items-center">
            <span>{{ dayjs(item.startTime).format('HH:mm:ss') }} - {{ dayjs(item.endTime).format('HH:mm:ss') }}</span>
            <el-tag type="info">          {{ formatDuration(dayjs(item.endTime).diff(dayjs(item.startTime))) }} </el-tag>
          </div>
          <div class="mt-6" text="text-secondary">{{ item.name }}</div>
        </div>
      </div>
    </el-scrollbar>
    <PlayRecordDialog v-if="monitorDialog" v-model="monitorDialog" :deviceId="data.deviceId" :channelId="data.channelId" :start-time="formData.startTime" :end-time="formData.endTime" />
  </div>
</template>

<style scoped lang="scss">
.active {
  background-color: var(--el-color-primary-9);
  border-color: var(--el-color-primary);
}
</style>
