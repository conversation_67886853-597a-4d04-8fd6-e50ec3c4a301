<script setup lang="ts">
import type { DeviceVO } from '@/api/monitor.ts'
import { MonitorAPI } from '@/api/monitor.ts'

const { data } = defineProps<{ data: DeviceVO }>()

/**
 * 操控云台
 */
const speed = ref(76)
const zoomSpeed = ref(1)
function control(direction: string) {
  const { deviceId, channelId } = data?.misc
  if (!deviceId)
    return
  MonitorAPI.ptzControl(deviceId, channelId, direction, speed.value, zoomSpeed.value).then((res) => {
    console.log('云台控制', res)
  })
}
</script>

<template>
  <header class="flex items-center gap-5 h-22">
    <i class="i-base-control" text="16 primary" />
    <span>操作控制</span>
  </header>
  <!--          方向控制按钮 -->
  <section m="t-36 b-16" class="w-198 h-198  m-auto gap-12" grid="~ cols-3 rows-3">
    <!--              左上 -->
    <i
      class="i-monitor-corner self-end justify-self-end cursor-pointer"
      text="41"
      @mousedown="control('upleft')"
      @mouseup="control('stop')"
      @touchstart="control('upleft')"
      @touchend="control('stop')"
    />
    <!--              上 -->
    <i
      class="i-monitor-side cursor-pointer"
      text="58"
      @mousedown="control('up')"
      @mouseup="control('stop')"
      @touchstart="control('up')"
      @touchend="control('stop')"
    />
    <!--              右上 -->
    <i
      class="i-monitor-corner rotate-90 self-end cursor-pointer"
      text="41"
      @mousedown="control('upright')"
      @mouseup="control('stop')"
      @touchstart="control('upright')"
      @touchend="control('stop')"
    />
    <!--              左 -->
    <i
      class="i-monitor-side -rotate-90 cursor-pointer"
      text="58"
      @mousedown="control('left')"
      @mouseup="control('stop')"
      @touchstart="control('left')"
      @touchend="control('stop')"
    />
    <!--              中间 -->
    <i class="i-monitor-center cursor-pointer" text="58" />
    <!--              右 -->
    <i
      class="i-monitor-side rotate-90 cursor-pointer"
      text="58"
      @mousedown="control('right')"
      @mouseup="control('stop')"
      @touchstart="control('right')"
      @touchend="control('stop')"
    />
    <!--              左下 -->
    <i
      class="i-monitor-corner -rotate-90 justify-self-end cursor-pointer"
      text="41"
      @mousedown="control('downleft')"
      @mouseup="control('stop')"
      @touchstart="control('downleft')"
      @touchend="control('stop')"
    />
    <!--              下 -->
    <i
      class="i-monitor-side rotate-180 cursor-pointer"
      text="58"
      @mousedown="control('down')"
      @mouseup="control('stop')"
      @touchstart="control('down')"
      @touchend="control('stop')"
    />
    <!--              右下 -->
    <i
      class="i-monitor-corner rotate-180 cursor-pointer"
      text="41"
      @mousedown="control('downright')"
      @mouseup="control('stop')"
      @touchstart="control('downright')"
      @touchend="control('stop')"
    />
  </section>
  <div class="leading-small" text="small text-regular">
    <span>缩放速度：</span>
    <span>{{ zoomSpeed }}</span>
  </div>
  <el-slider v-model="zoomSpeed" size="large" :min="0" :max="15" />
  <div class="leading-small" text="small text-regular">
    <span>速度：</span>
    <span>{{ speed }}</span>
  </div>
  <el-slider v-model="speed" size="large" :min="0" :max="255" />
  <header class="flex items-center gap-6 h-22 mb-16 mt-20">
    <i class="i-temp-monitor" text="14" />
    <span>预置位</span>
  </header>
  <section class="grid grid-cols-2 gap-10">
    <el-button class="flex-1">1.主入口</el-button>
    <el-button type="primary" class="flex-1 !ml-0">2.停车区域</el-button>
    <el-button class="flex-1 !ml-0">3.大厅中央</el-button>
    <el-button class="flex-1 !ml-0">4.楼梯口</el-button>
    <el-button class="flex-1 !ml-0">5.电梯区</el-button>
    <el-button class="flex-1 !ml-0">6.后门出口</el-button>
    <el-button class="flex-1 !ml-0">7.会议室</el-button>
    <el-button class="flex-1 !ml-0">8.办公区</el-button>
  </section>
</template>

<style scoped lang="scss">

</style>
