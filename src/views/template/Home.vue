<script setup lang="ts">
import avatar1 from '@/assets/temp/avatar1.png'
import avatar2 from '@/assets/temp/avatar2.png'
import avatar3 from '@/assets/temp/avatar3.png'
import avatar4 from '@/assets/temp/avatar4.png'
import oval2 from '@/assets/temp/oval2.png'
import oval3 from '@/assets/temp/oval3.png'
import oval4 from '@/assets/temp/oval4.png'
import weatherImg from '@/assets/temp/weather.png'

// 个人信息数据
const personalInfo = [
  { label: '手机', value: '+86 18800001234' },
  { label: '座机', value: '734567' },
  { label: '座位', value: '734567' },
  { label: '管理主体', value: '星落集团' },
  { label: '直属上级', value: 'RebeccaHuang' },
  { label: '职位', value: '高级销售' },
  { label: '入职时间', value: '2021-07-01' },
]

// 团队成员数据
const teamMembers = [
  { name: '<PERSON>', position: '部门 职位', avatar: avatar1 },
  { name: '罗伯特·肯尼迪', position: '前端开发 前台研发组员工', avatar: avatar2 },
  { name: 'Q乔峰', position: '技术产品 产品组员工', avatar: avatar3 },
  { name: '萧封', position: '产品运营 业务拓展组员工', avatar: avatar4 },
]

// 项目数据
const projects = [
  { name: '数字孪生区块链智能化项目', icon: oval2 },
  { name: '数字孪生区块链智能化项目', icon: oval3 },
  { name: '数字孪生区块链智能化项目', icon: oval4 },
]

// 图表标签页
const chartTabs = ref(['内容列表', '内容列表', '内容列表'])
const activeTab = ref(0)
</script>

<template>
  <div class="flex flex-col gap-10">
    <!-- 主内容区 -->
    <div class="flex gap-10">
      <!-- 左侧内容 -->
      <div class="flex-1 flex flex-col gap-10">
        <!-- 欢迎卡片 -->
        <div class="bg-white rounded-4 p-32 border border-border-lighter flex justify-between items-center">
          <div class="flex flex-col gap-10">
            <div class="flex items-center gap-16">
              <h1 class="text-20 font-semibold text-$el-text-color-primary">Hi, QingZhou</h1>
              <span class="text-14 text-$el-text-color-primary">下午好，清舟～</span>
            </div>
            <p class="text-13 text-$el-text-color-secondary">下午好，今天是你加入Vankey的第 100 天</p>
          </div>

          <!-- 天气信息 -->
          <div class="flex items-center gap-10">
            <img :src="weatherImg" alt="weather" class="w-88 h-88" />
            <div class="flex flex-col gap-8">
              <div class="text-32 text-$el-text-color-primary">27℃</div>
              <div class="flex gap-8 text-16 text-$el-text-color-regular">
                <span>2025-07-22</span>
                <span>星期一</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 个人信息卡片 -->
        <div class="bg-white rounded-4 border border-border-lighter">
          <div class="px-24 py-16 border-b border-$el-border-color flex justify-between items-center">
            <h2 class="text-20 font-semibold text-$el-text-color-primary">个人信息</h2>
            <button class="w-32 h-32 rounded-4 border border-$el-border-color flex items-center justify-center hover:bg-gray-50">
              <i class="i-ep-more text-14 text-$el-text-color-regular" />
            </button>
          </div>
          <div class="p-24">
            <div class="grid grid-cols-4 gap-y-16">
              <div v-for="(item, index) in personalInfo.slice(0, 4)" :key="index" class="flex flex-col gap-6">
                <span class="text-14 text-$el-text-color-primary">{{ item.label }}</span>
                <span class="text-14 text-$el-text-color-regular">{{ item.value }}</span>
              </div>
            </div>
            <div class="grid grid-cols-4 gap-y-16 mt-16">
              <div v-for="(item, index) in personalInfo.slice(4)" :key="index + 4" class="flex flex-col gap-6">
                <span class="text-14 text-$el-text-color-primary">{{ item.label }}</span>
                <span class="text-14 text-$el-text-color-regular">{{ item.value }}</span>
              </div>
              <div class="col-span-1 flex flex-col gap-6">
                <span class="text-14 text-$el-text-color-primary">所属团队</span>
                <span class="text-14 text-$el-text-color-regular">Star集团/Star公司/某事业群/某产品部/某运营中心/市场服务组</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 数据图表卡片 -->
        <div class="bg-white rounded-4 border border-border-lighter flex-1">
          <div class="px-24 py-16 border-b border-$el-border-color">
            <div class="flex gap-40 items-center">
              <div
                v-for="(tab, index) in chartTabs" :key="index"
                class="pb-16 cursor-pointer relative"
                :class="activeTab === index ? 'text-$el-text-color-primary font-semibold text-20' : 'text-$el-text-color-secondary text-18'"
                @click="activeTab = index"
              >
                {{ tab }}
                <div v-if="activeTab === index" class="absolute bottom-0 left-0 right-0 h-2 bg-$el-color-primary" />
              </div>
            </div>
          </div>

          <!-- 图表内容区 -->
          <div class="p-20 h-360">
            <div class="flex justify-between items-center mb-10">
              <div>
                <h3 class="text-16 font-medium text-$el-text-color-primary mb-4">多组散点图</h3>
                <p class="text-14 text-$el-text-color-regular">这里是注释内容</p>
              </div>
              <div class="flex items-center gap-8">
                <span class="text-12 text-$el-text-color-secondary">今天 04:23 更新</span>
                <div class="flex gap-8">
                  <button class="w-16 h-16 hover:opacity-70">
                    <i class="i-ep-download text-14 text-$el-text-color-regular" />
                  </button>
                  <button class="w-16 h-16 hover:opacity-70">
                    <i class="i-ep-full-screen text-14 text-$el-text-color-regular" />
                  </button>
                  <button class="w-16 h-16 hover:opacity-70">
                    <i class="i-ep-refresh text-14 text-$el-text-color-regular" />
                  </button>
                  <button class="w-16 h-16 hover:opacity-70">
                    <i class="i-ep-setting text-14 text-$el-text-color-regular" />
                  </button>
                </div>
              </div>
            </div>

            <!-- 图例 -->
            <div class="flex justify-end gap-16 mb-10">
              <div class="flex items-center gap-4">
                <span class="w-8 h-8 rounded-full bg-$el-color-primary" />
                <span class="text-12 text-$el-text-color-regular">图例</span>
              </div>
              <div class="flex items-center gap-4">
                <span class="w-8 h-8 rounded-full bg-$el-color-success" />
                <span class="text-12 text-$el-text-color-regular">图例</span>
              </div>
            </div>

            <!-- 图表占位 -->
            <div class="chart-placeholder">
              <svg viewBox="0 0 600 200" class="w-full h-full">
                <text x="50%" y="50%" text-anchor="middle" dy=".3em" fill="#909399">图表区域</text>
              </svg>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧边栏 -->
      <div class="w-300 flex flex-col gap-10">
        <!-- 个人卡片 -->
        <div class="bg-$el-color-primary rounded-6 p-32 pl-32 pr-45 py-48 h-274 user-card">
          <div class="flex flex-col gap-40">
            <!-- 头像 -->
            <div class="relative">
              <div class="w-80 h-80 rounded-full bg-white flex items-center justify-center">
                <span class="text-36 font-semibold text-$el-color-primary">T</span>
              </div>
              <div class="absolute bottom-0 right-0 w-24 h-24 bg-$el-color-primary rounded-full border-2 border-white flex items-center justify-center">
                <i class="i-ep-plus text-white text-12" />
              </div>
            </div>
            <!-- 用户信息 -->
            <div class="text-white">
              <h3 class="text-20 font-semibold mb-8 opacity-90">QingZhou</h3>
              <p class="text-14 opacity-90">XXXG 直客销售 业务拓展组员工</p>
            </div>
          </div>
        </div>

        <!-- 团队成员卡片 -->
        <div class="bg-white rounded-4 border border-border-lighter">
          <div class="px-24 py-16 border-b border-$el-border-color flex justify-between items-center">
            <h2 class="text-20 font-semibold text-$el-text-color-primary">团队成员</h2>
            <button class="w-32 h-32 rounded-4 border border-$el-border-color flex items-center justify-center hover:bg-gray-50">
              <i class="i-ep-more text-14 text-$el-text-color-regular" />
            </button>
          </div>
          <div class="p-24 flex flex-col gap-24">
            <div v-for="(member, index) in teamMembers" :key="index" class="flex items-center gap-16">
              <img :src="member.avatar" alt="avatar" class="w-48 h-48 rounded-full" />
              <div class="flex-1">
                <h4 class="text-14 font-semibold text-black text-opacity-90 mb-4">{{ member.name }}</h4>
                <p class="text-12 text-black text-opacity-40">{{ member.position }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 所属项目卡片 -->
        <div class="bg-white rounded-4 border border-border-lighter">
          <div class="px-24 py-16 border-b border-$el-border-color flex justify-between items-center">
            <h2 class="text-20 font-semibold text-$el-text-color-primary">所属项目</h2>
            <button class="w-32 h-32 rounded-4 border border-$el-border-color flex items-center justify-center hover:bg-gray-50">
              <i class="i-ep-more text-14 text-$el-text-color-regular" />
            </button>
          </div>
          <div class="p-24 flex flex-col gap-24">
            <div v-for="(project, index) in projects" :key="index" class="flex items-center gap-10">
              <img :src="project.icon" alt="icon" class="w-24 h-24" />
              <span class="text-14 text-black text-opacity-65">{{ project.name }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.user-card {
  box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.1);
}

.chart-placeholder {
  height: calc(100% - 80px);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
}
</style>
