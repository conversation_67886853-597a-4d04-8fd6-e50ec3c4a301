import type { AxiosResponse, InternalAxiosRequestConfig } from 'axios'
import axios from 'axios'
import router from '@/router'
import { useUserStoreHook } from '@/store/user'

export const TOKEN_KEY = 'VankeyAccessToken'

/**
 * 响应码枚举
 */
export enum ResultEnum {
  /**
   * 成功
   */
  SUCCESS = 200,

  /**
   * 令牌无效或过期
   */
  TOKEN_INVALID = 401,
}

// 创建 axios 实例
const service = axios.create({
  baseURL: import.meta.env.VITE_APP_BASE_API,
  timeout: 50000,
  headers: { 'Content-Type': 'application/json;charset=utf-8' },
})

// 保存所有的AbortController，用于取消请求
const abortControllerMap: Record<string, AbortController> = {}
// 请求拦截器
service.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const accessToken = localStorage.getItem(TOKEN_KEY)
    if (accessToken) {
      config.headers.Authorization = accessToken
    }

    /** 默认接口带有去重功能，若不需要去重，将headers中withoutUnique属性值设为true即可 */
    if (!config.headers.withoutUnique) {
      // 取消重复请求
      const key = config.url
      if (abortControllerMap[key]) {
        abortControllerMap[key].abort()
        abortControllerMap[key] = undefined
      }

      const controller = new AbortController()
      abortControllerMap[key] = controller
      config.signal = controller.signal
    }

    return config
  },
  (error: any) => {
    return Promise.reject(error)
  },
)

// 响应拦截器
let isLogOuting = false
service.interceptors.response.use(
  (response: AxiosResponse) => {
    // 检查配置的响应类型是否为二进制类型（'blob' 或 'arraybuffer'）, 如果是，直接返回响应对象
    if (response.config.responseType === 'blob' || response.config.responseType === 'arraybuffer') {
      return response
    }

    const { code, data, msg } = response.data
    if (code === ResultEnum.SUCCESS) {
      isLogOuting = false
      return data
    }

    ElMessage.error({ message: msg || '系统出错', grouping: true })
    return Promise.reject(response.data || 'Error')
  },
  (error: any) => {
    if (error.code === 'ERR_CANCELED')
      return
    // 异常处理
    if (error.response.data) {
      const { code, msg } = error.response.data
      if (code === ResultEnum.TOKEN_INVALID) {
        if (isLogOuting)
          return
        isLogOuting = true
        ElMessage.error({ message: '您的会话已过期，请重新登录', grouping: true })
        // 跳转到登录页，保留当前路由用于登录后跳转
        const userStore = useUserStoreHook()
        const currentPath = router.currentRoute.value.fullPath
        userStore.clearAppState({ redirect: currentPath })
      }
      else {
        ElMessage.error({ message: msg || '系统出错', grouping: true })
      }
    }

    return Promise.reject(error.message)
  },
)

// 导出 axios 实例
export default service
