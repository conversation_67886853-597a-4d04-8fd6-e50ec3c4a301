<script setup lang="ts">
import { useEventListener } from '@vueuse/core'
import { storeToRefs } from 'pinia'
import { storageKey } from '@/constants'
import { useAppStore } from '@/store/app.ts'

const appStore = useAppStore()
const { theme } = storeToRefs(appStore)

function toggleDark(themeName: string) {
  document.documentElement.classList.remove(theme.value)
  document.documentElement.classList.add(themeName)
  theme.value = themeName
  localStorage.setItem(storageKey.theme, themeName)
}

if (theme.value) {
  document.documentElement.classList.add(theme.value)
}

function toggleTheme(event: PointerEvent, themeName: string) {
  // 兼容性处理
  if (!document.startViewTransition) {
    toggleDark(themeName)
    return
  }

  /**
   * 主题切换时的过度动画
   */
  const switchEl = event.target as HTMLElement
  const rect = switchEl.getBoundingClientRect()
  const x = rect.left + rect.width / 2
  const y = rect.top + rect.height / 2
  const endRadius = Math.hypot(
    Math.max(x, innerWidth - x),
    Math.max(y, innerHeight - y),
  )

  const transition = document.startViewTransition(async () => {
    toggleDark(themeName)
  })

  transition.ready.then(() => {
    const clipPath = [
      `circle(0px at ${x}px ${y}px)`,
      `circle(${endRadius}px at ${x}px ${y}px)`,
    ]
    document.documentElement.animate(
      {
        clipPath: theme.value === 'dark' ? [...clipPath].toReversed() : clipPath,
      },
      {
        duration: 400,
        easing: 'ease-in',
        pseudoElement: theme.value === 'dark'
          ? '::view-transition-old(root)'
          : '::view-transition-new(root)',
      },
    )
  })
}

/**
 * 监听系统主题变化
 */
const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
function updateTheme() {
  const shouldBeDark = mediaQuery.matches
  const currentIsDark = theme.value === 'dark'
  if (currentIsDark !== shouldBeDark) {
    const newTheme = shouldBeDark ? 'dark' : 'light'
    document.documentElement.classList.remove(theme.value)
    document.documentElement.classList.add(newTheme)
    theme.value = newTheme
    localStorage.setItem(storageKey.theme, newTheme)
  }
}

if (!localStorage.getItem(storageKey.theme)) {
  updateTheme()
}

useEventListener(mediaQuery, 'change', () => {
  updateTheme()
})
</script>

<template>
  <div class="cursor-pointer" text="20">
    <i v-if="theme === 'light'" class="i-base-sun" text="primary" @click="toggleTheme($event, 'dark')" />
    <i v-else class="i-base-night" text="#caced3" @click="toggleTheme($event, 'light')" />
  </div>
</template>

<style scoped lang="scss">

</style>
