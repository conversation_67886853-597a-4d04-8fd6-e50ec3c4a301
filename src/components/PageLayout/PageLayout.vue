<script setup lang="ts"></script>

<template>
  <div class="transition-area">
    <!--    左侧 -->
    <aside class="left relative pointer-events-auto flex flex-col">
      <!--      <transition -->
      <!--        enter-active-class="animate__animated animated animate__flipInY" -->
      <!--        leave-active-class="animate__animated animated animate__flipOutY" -->
      <!--        mode="out-in" -->
      <!--      > -->
      <transition
        enter-active-class="animate__animated animated animate__fadeInLeft"
        leave-active-class="animate__animated animated animate__fadeOutLeft"
        mode="out-in"
      >
        <!--      <transition name="el-zoom-in-center" mode="out-in"> -->
        <slot name="left" />
      </transition>
    </aside>
    <!--    中间 -->
    <section class="center">
      <header class="header">
        <slot name="center-header" />
      </header>
      <footer class="pointer-events-auto footer">
        <transition
          enter-active-class="animate__animated animated animate__fadeInUp"
          leave-active-class="animate__animated animated animate__fadeOutDown"
          mode="out-in"
        >
          <slot name="center-footer" />
        </transition>
      </footer>
    </section>
    <!--    右侧 -->
    <aside class="relative pointer-events-auto flex flex-col right">
      <!--      右侧操作按钮 -->
      <div class="absolute -left-10 -translate-x-full top-10 pointer-events-none">
        <slot name="right-control" />
      </div>
      <transition
        enter-active-class="animate__animated animated animate__fadeInRight"
        leave-active-class="animate__animated animated animate__fadeOutRight"
        mode="out-in"
      >
        <!--      <transition name="el-zoom-in-center" mode="out-in"> -->
        <slot name="right" />
      </transition>
    </aside>
  </div>
</template>

<style scoped lang="scss">
.animated {
  animation-duration: 0.3s; //动画持续时间
}
</style>
