<script setup lang="ts">
import type { ImageProps } from 'element-plus'
import { getFileUrl } from '@/api/file.ts'

const { src, previewSrcList, fit = 'cover', hideOnClickModal = true, previewTeleported = true } = defineProps<Partial<ImageProps>>()

const addressableSrc = ref('')
watch(
  () => src,
  () => {
    if (!src)
      return
    getFileUrl(src).then((url) => {
      addressableSrc.value = url
    })
  },
  { immediate: true },
)

const addressablePreviewSrcList = ref([])
watch(
  () => previewSrcList,
  () => {
    if (!previewSrcList) {
      getFileUrl(src).then((url) => {
        addressablePreviewSrcList.value = [url]
      })
      return
    }

    addressablePreviewSrcList.value = []
    for (const [index, url] of previewSrcList.filter(Boolean).entries()) {
      getFileUrl(url).then((res) => {
        addressablePreviewSrcList.value[index] = res
      })
    }
  },
  { immediate: true },
)
</script>

<template>
  <el-image
    v-if="addressableSrc"
    :fit="fit"
    v-bind="$attrs"
    :src="addressableSrc"
    :preview-teleported="previewTeleported"
    :hide-on-click-modal="hideOnClickModal"
    :preview-src-list="addressablePreviewSrcList"
  >
    <template #error>
      <slot name="error" />
    </template>
    <template #placeholder>
      <slot name="placeholder" />
    </template>
  </el-image>
</template>

<style scoped lang="scss"></style>
