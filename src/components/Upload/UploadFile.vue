<script lang="ts" setup>
import type { UploadFile, UploadRequestOptions, UploadUserFile } from 'element-plus'
import { Upload } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { ref } from 'vue'
import { FileAPI, getFileUrl } from '@/api/file.ts'
import { isImg } from '@/utils'

const {
  dirName = 'file',
  accept = '',
  tip,
  limit = 6,
} = defineProps<{
  /** 上传文件目录名 */
  dirName?: string
  accept?: string
  tip?: string
  limit?: number
}>()

const emit = defineEmits<{
  (e: 'change', fileList: UploadUserFile[]): void
}>()

const rawFiles = defineModel<UploadUserFile[]>()

const fileList = ref<UploadUserFile[]>([])

function changeValue() {
  needRefresh = false
  rawFiles.value = fileList.value.filter(item => item.status === 'success').map(item => ({ name: item.name, url: item.objectName, size: item.size }))
  emit('change', rawFiles.value)
}

const loading = ref(false)
function upload(options: UploadRequestOptions) {
  const file = options.file
  loading.value = true
  const fileItem = fileList.value.find(file => file.uid == options.file.uid)
  fileItem.status = 'uploading'
  return FileAPI.upload(file, dirName)
    .then((res) => {
      fileItem.status = 'success'
      fileItem.url = res.url
      fileItem.objectName = res.objectName
      changeValue()
    })
    .catch(() => {
      const fileIndex = fileList.value.findIndex(file => file.uid == options.file.uid)
      fileList.value.splice(fileIndex, 1)
    })
    .finally(() => {
      loading.value = false
    })
}

/**
 * 删除图片
 */
// function remove(removeFile: UploadFile) {
//   const fileItem = fileList.value.find(file => file.uid == removeFile.uid)
//   if (fileItem) {
//     FileAPI.remove(fileItem.objectName)
//       .then(() => {
//         changeValue()
//       })
//       .finally(() => {})
//   }
// }
function remove() {
  nextTick(() => {
    changeValue()
  })
}

/** 文件上传超过限制提示 */
function handleExceed() {
  ElMessage.warning(`最多上传 ${limit} 个文件，超出了文件数量限制！`)
}

/**
 * 预览
 */
const showViewer = ref(false)
const previewImgList = ref([])
function preview(file: UploadFile) {
  if (isImg(file.name)) {
    previewImgList.value = [file.url]
    showViewer.value = true
  }
  else {
    window.open(file.url)
  }
}

/**
 * 反显图片
 */
/** 内部更新rawFiles时这里无需执行 */
let needRefresh = true
watch(
  rawFiles,
  () => {
    if (!needRefresh) {
      needRefresh = true
      return
    }

    if (!rawFiles.value?.length) {
      fileList.value = []
      return
    }

    fileList.value = []
    for (const item of rawFiles.value) {
      getFileUrl(item.url).then((url) => {
        fileList.value.push({ objectName: item.url, name: item.name, url, status: 'success' })
      })
    }
  },
  { immediate: true },
)

/**
 * 供外部调用，判断是否全部上传成功
 */
function uploaded() {
  return !fileList.value.some(item => item.status === 'uploading')
}

defineExpose({ uploaded })
</script>

<template>
  <el-upload
    v-model:file-list="fileList"
    :http-request="upload"
    multiple
    :on-preview="preview"
    :limit="limit"
    :accept="accept"
    :on-remove="remove"
    :on-exceed="handleExceed"
    class="w-full"
  >
    <el-button :loading="loading" :icon="Upload" type="primary"> 上传 </el-button>
    <template #tip>
      <div text="12 #8290AC">{{ tip }}</div>
    </template>
    <!-- 图片预览 -->
    <el-image-viewer v-if="showViewer" :teleported="true" hide-on-click-modal :url-list="previewImgList" @close="showViewer = false" />
  </el-upload>
</template>

<style lang="scss" scoped></style>
