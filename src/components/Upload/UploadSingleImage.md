# UploadSingleImage 单图片上传组件

## 组件介绍

UploadSingleImage 是一个基于 Element Plus 封装的单图片上传组件。

### 为什么需要单独的 UploadSingleImage 组件

- 专为单图场景设计(后端接收的是字符串的情况)
- 数据模型更简单：单图场景的 v-model 为字符串（objectName），而不是数组。与后端通常以字符串字段存储头像/封面更契合，避免“取数组第一项”的模板样板与空数组边界错误。
- 交互语义更明确：仅展示一个占位/缩略图区域，点击即可替换或预览；省去多图组件的卡片墙、排序、批量删除等冗余交互，更贴合头像、封面等单图需求。

## 基础用法

```vue
<script setup>
// v-model 绑定值为字符串（服务器存储标识 objectName）
const avatar = ref('')
</script>

<template>
  <UploadSingleImage v-model="avatar" tip="支持 jpg/png 等格式，建议尺寸 200x200" />
</template>
```

## Props 属性

| 属性名  | 类型   | 默认值  | 说明                               |
| ------- | ------ | ------- | ---------------------------------- |
| dirName | string | 'image' | 上传目录名，用于后端按目录分类存储 |
| tip     | string | -       | 上传区域下方的提示文案             |

## v-model

- 绑定值类型：`string`
- 含义：图片在服务器上的存储标识（objectName），非完整 URL。
- 组件内部会根据该标识通过 `getFileUrl(objectName)` 拉取实际可访问的图片 URL 用于展示。

## Events 事件

| 事件名 | 说明           | 回调参数                                       |
| ------ | -------------- | ---------------------------------------------- |
| change | 图片变更时触发 | (fileUrl: string) 服务器存储标识（objectName） |

## 暴露方法

| 方法名   | 说明                               | 返回值  |
| -------- | ---------------------------------- | ------- |
| uploaded | 判断是否上传完成（无进行中的上传） | boolean |

## 功能特性

1. 单图上传：适合头像、封面等场景。
2. 上传状态：内置加载状态，上传中会禁用重复提交。
3. 外部可通过 `uploaded()` 判断是否还有进行中的上传任务。
4. 图片回显：编辑场景下，直接给 `v-model` 赋值为已存储的 `objectName` 即可回显。
5. 展示尺寸：默认图片展示区域使用原子化类名 `w-150 h-150 rounded-6`（150px x 150px，圆角 6px）。

## 完整示例

### 基础单图上传（头像）

```vue
<script setup>
const avatar = ref('')
</script>

<template>
  <UploadSingleImage v-model="avatar" tip="建议尺寸 200x200，支持 JPG/PNG" />
</template>
```

## 样式定制

- 组件默认使用原子化类名 `w-150 h-150 rounded-6` 作为展示尺寸。
- 如需修改展示尺寸，可在外层容器约束宽高，或通过深度选择器覆盖内部图片样式：

```scss
:deep(.image-card) {
  width: 120px;
  height: 120px;
  border-radius: 8px;
}

```
