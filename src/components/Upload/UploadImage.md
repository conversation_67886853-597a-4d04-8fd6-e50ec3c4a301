# UploadImage 多图片上传组件

## 组件介绍

UploadImage 是一个基于 Element Plus 封装的多图片上传组件。

## 基础用法

```vue
<script setup>
const imageList = ref([])
</script>

<template>
  <UploadImage v-model="imageList" />
</template>
```

## Props 属性

| 属性名  | 类型   | 默认值  | 说明                                       |
| ------- | ------ | ------- | ------------------------------------------ |
| dirName | string | 'image' | 上传图片的目录名，用于服务器端图片分类存储 |
| tip     | string | -       | 上传提示信息，显示在图片列表下方           |
| limit   | number | 6       | 最大上传图片数量限制                       |

## v-model

- 组件支持 `v-model` 双向绑定，绑定值类型为 `UploadUserFile[]`：

```typescript
interface UploadUserFile {
  name: string // 文件名
  url: string // 文件的 objectName（服务器存储路径）
  size?: number // 文件大小
}
```

- 含义：图片在服务器上的存储标识（objectName），非完整 URL。
- 组件内部会根据该标识通过 `getFileUrl(objectName)` 拉取实际可访问的图片 URL 用于展示。

## Events 事件

| 事件名 | 说明               | 回调参数                     |
| ------ | ------------------ | ---------------------------- |
| change | 图片列表变化时触发 | (fileList: UploadUserFile[]) |

## 暴露方法

| 方法名   | 说明                 | 返回值  |
| -------- | -------------------- | ------- |
| uploaded | 判断图片是否上传完成 | boolean |

## 功能特性

### 1. 图片预览

点击图片可以使用内置的图片查看器进行大图预览。

### 2. 图片回显

支持编辑场景下的图片回显，自动从服务器获取图片的访问地址。

### 3. 支持查看是否上传完成

通过 `uploaded` 方法可以判断所有图片是否上传完成，方便在表单提交前进行校验。

## 完整示例

### 基础多图上传

```vue
<script setup>
const imageList = ref([])
</script>

<template>
  <UploadImage v-model="imageList" />
</template>
```

### 自定义上传数量

```vue
<script setup>
const productImages = ref([])
</script>

<template>
  <UploadImage
    v-model="productImages"
    dirName="products"
    :limit="9"
    tip="产品图片，最多上传9张"
  />
</template>
```

### 表单中使用

```vue
<script setup>
const formData = ref({
  images: [],
})

function submitForm() {
  // 提交表单数据
  console.log('商品图片：', formData.value.images)
}
</script>

<template>
  <el-form :model="formData" label-width="5vw">
    <el-form-item label="商品图片">
      <UploadImage
        v-model="formData.images"
        dirName="goods"
        :limit="5"
        tip="请上传商品图片，最多5张"
      />
    </el-form-item>
    <el-form-item>
      <el-button type="primary" @click="submitForm">提交</el-button>
    </el-form-item>
  </el-form>
</template>
```

###图片回显

```vue
<script setup>
const uploadRef = useTemplateRef('uploadRef')
const imageList = ref([])

function handleQuery() {
  // 模拟从接口获取已上传的图片
  getProductDetail().then((res) => {
    // 设置图片列表，组件会自动获取图片的访问地址
    imageList.value = res.images // [{name: 'img1.jpg', url: 'path/to/img1.jpg'}]
  })
}

handleQuery()

function checkUploadStatus() {
  const allUploaded = uploadRef.value.uploaded()
  if (allUploaded) {
    ElMessage.success('所有图片已上传完成')
  }
  else {
    ElMessage.warning('还有图片正在上传中')
  }
}
</script>

<template>
  <UploadImage
    ref="uploadRef"
    v-model="imageList"
  />
  <el-button @click="checkUploadStatus">检查上传状态</el-button>
</template>
```

## 样式自定义

组件预留了样式调整的注释代码，可以根据需要调整图片卡片的尺寸：

```scss
// 修改图片上传器尺寸
:deep(.el-upload) {
  --el-upload-picture-card-size: 50px;
}
:deep(.el-upload-list) {
  --el-upload-list-picture-card-size: 50px;
}
:deep(.el-upload-list--picture-card .el-upload-list__item-actions span + span) {
  margin-left: 3px !important;
}

```

## Todo

- **图片大小限制**
