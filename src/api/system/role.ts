import request from '@/utils/request'

export const RoleAPI = {
  // 角色分页列表
  pageList(params: PageRequest) {
    return request<any, Records<Role>>({
      url: `/api/v1/manage/web/roles/page`,
      method: 'get',
      params,
    })
  },

  // 角色下拉
  options() {
    return request<any, any>({
      url: `/api/v1/manage/web/roles/options`,
      method: 'get',
    })
  },

  // 菜单列表
  roleOptions() {
    return request<any, any>({
      url: `/api/v1/manage/web/menus/options`,
      method: 'get',
    })
  },

  // 新增角色
  add(data: Role) {
    return request<any, any>({
      url: `/api/v1/manage/web/roles`,
      method: 'post',
      data,
    })
  },

  // 获取角色信息
  info(roleId: string) {
    return request<any, any>({
      url: `/api/v1/manage/web/roles/${roleId}/form`,
      method: 'get',
    })
  },

  // 修改角色
  update(data: Role) {
    return request<any, any>({
      url: `/api/v1/manage/web/roles/${data.id}`,
      method: 'put',
      data,
    })
  },

  // 删除角色
  delete(ids) {
    return request<any, any>({
      url: `/api/v1/manage/web/roles/${ids}`,
      method: 'delete',
    })
  },

  // 修改角色状态
  enabled(roleId: string, enabled: boolean) {
    return request<any, any>({
      url: `/api/v1/manage/web/roles/${roleId}/enabled`,
      method: 'put',
      params: { enabled },
    })
  },

  // 获取角色拥有的权限
  getMenus(roleId: string) {
    return request<any, any>({
      url: `/api/v1/manage/web/roles/${roleId}/menuIds`,
      method: 'get',
    })
  },

  // 修改角色权限
  updateMenus(roleId: string, menus: Array<number>) {
    return request<any, any>({
      url: `/api/v1/manage/web/roles/${roleId}/menus`,
      method: 'put',
      data: menus,
    })
  },
}

export interface PageRequest {
  /**
   * 页码
   */
  pageNum?: number
  /**
   * 每页记录数
   */
  pageSize?: number
  /**
   * 角色名称
   */
  roleName?: string
}

export interface Role {
  loading?: boolean
  loading2?: boolean
  roleName?: string
  id?: string
  name?: string
  enabled?: boolean
  createTime?: string
  modifyTime?: string
  modifyBy?: string
}
