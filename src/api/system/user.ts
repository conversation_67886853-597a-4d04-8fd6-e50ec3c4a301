import request from '@/utils/request'

export const UserAPI = {
  /**
   * 获取当前登录用户信息
   *
   * @returns 登录用户昵称、头像信息，包括角色和权限
   */
  getInfo() {
    return request<any, UserInfo>({
      url: `/api/v1/manage/web/users/me`,
      method: 'get',
    })
  },
  /**
   * 获取菜单路由列表
   *
   * @returns 登录用户昵称、头像信息，包括角色和权限
   */
  routes() {
    return request<any, UserInfo>({
      url: `/api/v1/manage/web/menus/routes`,
      method: 'get',
    })
  },

  // 部门人员树
  getDeptUserTree() {
    return request<any, any>({
      url: `/api/v1/dept/optionsDeptUser`,
      method: 'get',
    })
  },

  // 用户分页列表
  pageList(params: User) {
    return request<any, Records<User>>({
      url: `/api/v1/manage/web/users/page`,
      method: 'get',
      params,
    })
  },

  // 用户启用禁用
  enabled(userId: string, enabled: boolean) {
    return request<any, any>({
      url: `/api/v1/manage/web/users/${userId}/enabled`,
      method: 'patch',
      params: { enabled },
    })
  },

  // 新增用户
  add(data: User) {
    return request<any, any>({
      url: `/api/v1/manage/web/users`,
      method: 'post',
      data,
    })
  },

  // 修改用户
  update(data: User) {
    return request<any, any>({
      url: `/api/v1/manage/web/users`,
      method: 'put',
      data,
    })
  },

  // 删除用户
  delete(ids) {
    return request<any, any>({
      url: `/api/v1/manage/web/users/${ids}`,
      method: 'delete',
    })
  },
}

/** 登录用户信息 */
export interface UserInfo {
  /** 用户ID */
  userId?: number

  /** 用户名 */
  username?: string

  /** 昵称 */
  nickname?: string

  /** 头像URL */
  avatar?: string

  /** 角色 */
  roles?: string[]

  /** 按钮权限 */
  perms?: string[]

  /** 菜单权限 */
  menus?: string[]
}

/**
 * 用户分页视图对象
 *
 * UserPageVO
 */
export interface User {
  /**
   * 是否超管
   */
  admin?: boolean
  avatar?: string
  gender?: number
  id?: number
  modifyBy?: string
  modifyTime?: string
  nickName?: string
  roleNames?: string
  /**
   * 用户状态
   */
  enabled?: boolean
  /**
   * 创建时间-结束时间
   */
  endTime?: string
  /**
   * 关键字
   */
  keywords?: string
  /**
   * 姓名
   */
  nickname?: string
  /**
   * 页码
   */
  pageNum?: number
  /**
   * 每页记录数
   */
  pageSize?: number
  /**
   * 手机号
   */
  phone?: string
  /**
   * 角色
   */
  roleName?: string
  /**
   * 创建时间-开始时间
   */
  startTime?: string
  /**
   * 账号
   */
  username?: string
}
