import request from '@/utils/request'

const AUTH_BASE_URL = '/api/v1/auth'

const AuthAPI = {
  /** 登录 接口 */
  login(data: LoginData) {
    const formData = new FormData()
    formData.append('authType', data.authType)
    if (data.authType == '0') {
      formData.append('username', data.username)
      formData.append('password', data.password)
      formData.append('captchaKey', data.captchaKey)
      formData.append('captchaCode', data.captchaCode)
    }
    else {
      formData.append('phone', data.phone)
      formData.append('phoneCode', data.phoneCode)
      formData.append('captchaKey', data.captchaKey)
      formData.append('captchaCode', data.captchaCode)
    }

    return request<any, LoginResult>({
      url: '/api/v1/manage/auth/login',
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    })
  },

  /** 注销 接口 */
  logout() {
    return request({
      url: `${AUTH_BASE_URL}/manage/logout`,
      method: 'delete',
    })
  },

  /** 获取验证码 接口 */
  getCaptcha() {
    return request<any, CaptchaResult>({
      url: `${AUTH_BASE_URL}/captcha`,
      method: 'get',
    })
  },

  /** 发送手机验证码 接口 */
  setPhoneCode(data: SetCode) {
    return request<any, CaptchaResult>({
      url: `${AUTH_BASE_URL}/phone-code/${data.type}?phone=${data.phone}`,
      method: 'POST',
    })
  },
  /** 忘记密码 获取手机验证码 */
  checkPhoneCode(data: CheckCode) {
    return request<any, any>({
      url: `${AUTH_BASE_URL}/phone-code/${data.type}?phone=${data.phone}&phoneCode=${data.phoneCode}`,
      method: 'get',
    })
  },
  /** 重置密码  */
  resetCaptcha(data: ForgetPassword) {
    return request<any, CaptchaResult>({
      url: `/api/v1/manage/web/users/forget-password`,
      method: 'put',
      data,
    })
  },
  /** 修改密码  */
  modifyCaptcha(data: any) {
    return request<any, any>({
      url: `/api/v1/manage/web/users/password`,
      method: 'put',
      data,
    })
  },
}

export default AuthAPI

/** 登录请求参数 */
export interface LoginData {
  /** 用户名 */
  username?: string
  /** 密码 */
  password?: string
  /** 登录类型  authType 0 密码登录 1 手机号登录 */
  authType?: string
  /** 手机号 */
  phone?: string
  /** 手机验证码 */
  phoneCode?: string
  /** 验证码 */
  captchaCode?: string
  /** 验证码缓存key */
  captchaKey?: string
}

/** 登录响应 */
export interface LoginResult {
  /** 访问token */
  accessToken?: string
  /** 过期时间(单位：毫秒) */
  expires?: number
  /** 刷新token */
  refreshToken?: string
  /** token 类型 */
  tokenType?: string
}

/** 验证码响应 */
export interface CaptchaResult {
  /** 验证码缓存key */
  captchaKey: string
  /** 验证码图片Base64字符串 */
  captchaBase64: string
}

/** 请求验证码 */
export interface SetCode {
  /** 手机号 */
  phone: string | number
  /** 请求类型 */
  type: 'login' | 'forget-password'
}

/** 验证验证码 */
export interface CheckCode {
  /** 手机号 */
  phone: string | number
  /** 手机验证码 */
  phoneCode: string | number
  /** 请求类型 */
  // type: 'login-register' | 'forget-password'
  type: string
}

/**
 * 忘记密码表单
 */
export interface ForgetPassword {
  /**
   * 确认密码
   */
  confirmPassword?: string
  /**
   * 密码
   */
  password?: string
  /**
   * 手机号
   */
  phone?: string
  /**
   * 手机验证码
   */
  phoneCode?: string
}
