import request from '@/utils/request.ts'

export const MonitorAPI = {
  page(params: PageQuery<Monitor>) {
    return request<any, Records<Monitor>>({
      url: '/api/v1/manage/web/device/camera/page',
      method: 'get',
      params,
    })
  },

  detail(deviceId: string) {
    return request<any, DeviceVO>({
      url: `/api/v1/manage/web/device/${deviceId}/base`,
      method: 'get',
    })
  },

  // 播放
  play(deviceId: string, channelId: string) {
    return request<any, Play>({
      url: `/api/v1/manage/web/camera/control/play/start/${deviceId}/${channelId}`,
      method: 'get',
    })
  },

  // 云台控制
  ptzControl(deviceId: string, channelId: string, command: string, speed: number = 76, zoomSpeed: number = 1) {
    return request<any, any>({
      url: `/api/v1/manage/web/camera/control/front-end/ptz/${deviceId}/${channelId}`,
      method: 'get',
      params: {
        command,
        horizonSpeed: speed,
        verticalSpeed: speed,
        zoomSpeed,
      },
    })
  },

  // 设备录像查询
  deviceRecord(deviceId: string, channelId: string, startTime: string, endTime: string) {
    return request<any, DeviceRecord>({
      url: `/api/v1/manage/web/camera/control/gb_record/query/${deviceId}/${channelId}`,
      method: 'get',
      params: { startTime, endTime },
    })
  },

  // 视频回放
  videoReplay(deviceId: string, channelId: string, startTime: string, endTime: string) {
    return request<any, Play>({
      url: `/api/v1/manage/web/camera/control/playback/start/${deviceId}/${channelId}`,
      method: 'get',
      params: { startTime, endTime },
    })
  },

}

/**
 * CameraVO
 */
export interface Monitor {
  /**
   * 区域名称
   */
  areaName?: string
  /**
   * 批次号
   */
  batchNum?: string
  /**
   * 通道号
   */
  channelId?: string
  /**
   * 通道名称
   */
  channelName?: string
  /**
   * 客户id
   */
  customerId?: number
  /**
   * 客户名称
   */
  customerName?: string
  /**
   * 设备号
   */
  deviceId?: string
  /**
   * 对外型号名称
   */
  externalModel?: string
  id?: number
  /**
   * 设备型号id
   */
  innerModel?: number
  /**
   * 设备型号名称
   */
  innerModelName?: string
  /**
   * 修改时间
   */
  modifyTime?: string
  /**
   * 是否在线
   */
  online?: boolean
  /**
   * 设备编码(系统内部编号)
   */
  serialNum?: string
  /**
   * 快照URL
   */
  snapUrl?: string
  /**
   * 设备类型
   * 1 杀虫灯
   * 2 墒情
   * 3 虫情分析
   * 4 监控
   */
  type?: number
  /**
   * 设备类型名称
   * 映射json value
   */
  typeName?: string
}

/**
 * StreamContent
 *
 * 转码后的视频流
 */
export interface Play {
  /**
   * 应用名
   */
  app?: string
  /**
   * 文件下载地址（录像下载使用）
   */
  downLoadFilePath?: DownloadFileInfo
  /**
   * 结束时间
   */
  endTime?: string
  /**
   * HTTP-FLV流地址
   */
  flv?: string
  /**
   * HTTP-FMP4流地址
   */
  fmp4?: string
  /**
   * HLS流地址
   */
  hls?: string
  /**
   * HTTPS-FLV流地址
   */
  https_flv?: string
  /**
   * HTTPS-FMP4流地址
   */
  https_fmp4?: string
  /**
   * HTTPS-HLS流地址
   */
  https_hls?: string
  /**
   * HTTPS-TS流地址
   */
  https_ts?: string
  /**
   * IP
   */
  ip?: string
  /**
   * 流编码信息
   */
  mediaInfo?: MediaInfo
  /**
   * 流媒体ID
   */
  mediaServerId?: string
  progress?: number
  /**
   * RTC流地址
   */
  rtc?: string
  /**
   * RTCS流地址
   */
  rtcs?: string
  /**
   * RTMP流地址
   */
  rtmp?: string
  /**
   * RTMPS流地址
   */
  rtmps?: string
  /**
   * RTSP流地址
   */
  rtsp?: string
  /**
   * RTSPS流地址
   */
  rtsps?: string
  /**
   * 开始时间
   */
  startTime?: string
  /**
   * 流ID
   */
  stream?: string
  /**
   * 转码后的视频流
   */
  transcodeStream?: StreamContent
  /**
   * HTTP-TS流地址
   */
  ts?: string
  /**
   * Websocket-FLV流地址
   */
  ws_flv?: string
  /**
   * Websocket-FMP4流地址
   */
  ws_fmp4?: string
  /**
   * Websocket-HLS流地址
   */
  ws_hls?: string
  /**
   * Websocket-TS流地址
   */
  ws_ts?: string
  /**
   * Websockets-FLV流地址
   */
  wss_flv?: string
  /**
   * Websockets-FMP4流地址
   */
  wss_fmp4?: string
  /**
   * Websockets-HLS流地址
   */
  wss_hls?: string
  /**
   * Websockets-TS流地址
   */
  wss_ts?: string
}

/**
 * 文件下载地址（录像下载使用）
 *
 * DownloadFileInfo
 */
export interface DownloadFileInfo {
  httpDomainPath?: string
  httpPath?: string
  httpsDomainPath?: string
  httpsPath?: string
}

/**
 * 流编码信息
 *
 * MediaInfo
 */
export interface MediaInfo {
  /**
   * 存活时间，单位秒
   */
  aliveSecond?: number
  /**
   * 应用名
   */
  app?: string
  /**
   * 音频通道数
   */
  audioChannels?: number
  /**
   * 音频编码类型
   */
  audioCodec?: string
  /**
   * 音频采样率
   */
  audioSampleRate?: number
  /**
   * 数据产生速度，单位byte/s
   */
  bytesSpeed?: number
  /**
   * 鉴权参数
   */
  callId?: string
  /**
   * 音频采样率
   */
  duration?: number
  /**
   * FPS
   */
  fps?: number
  /**
   * 视频高度
   */
  height?: number
  /**
   * 丢包率
   */
  loss?: number
  /**
   * 在线
   */
  online?: boolean
  /**
   * unknown =
   * 0,rtmp_push=1,rtsp_push=2,rtp_push=3,pull=4,ffmpeg_pull=5,mp4_vod=6,device_chn=7,rtc_push=8
   */
  originType?: number
  /**
   * originType的文本描述
   */
  originTypeStr?: string
  /**
   * 产生流的源流地址
   */
  originUrl?: string
  /**
   * 额外参数
   */
  paramMap?: MapString
  /**
   * 观看人数
   */
  readerCount?: number
  /**
   * 协议
   */
  schema?: string
  /**
   * 服务ID
   */
  serverId?: string
  /**
   * 流ID
   */
  stream?: string
  /**
   * 视频编码类型
   */
  videoCodec?: string
  /**
   * 视频宽度
   */
  width?: number
}

/**
 * 额外参数
 *
 * MapString
 */
export interface MapString {
  key?: string
}

/**
 * RecordInfo
 */
export interface DeviceRecord {
  /**
   * 通道编号
   */
  channelId?: string
  count?: number
  /**
   * 设备编号
   */
  deviceId?: string
  lastTime?: Instant
  /**
   * 设备名称
   */
  name?: string
  recordList?: RecordItem[]
  /**
   * 命令序列号
   */
  sn?: string
  /**
   * 列表总数
   */
  sumNum?: number
}

/**
 * Instant
 */
export interface Instant {
  /**
   * The number of nanoseconds, later along the time-line, from the seconds field.
   * This is always positive, and never exceeds 999,999,999.
   */
  nanos?: number
  /**
   * The number of seconds from the epoch of 1970-01-01T00:00:00Z.
   */
  seconds?: number
}

/**
 * 设备录像详情
 *
 * RecordItem
 */
export interface RecordItem {
  /**
   * 录像地址(可选)
   */
  address?: string
  /**
   * 设备编号
   */
  deviceId?: string
  /**
   * 录像结束时间(可选)
   */
  endTime?: string
  /**
   * 文件路径名 (可选)
   */
  filePath?: string
  /**
   * 录像文件大小,单位:Byte(可选)
   */
  fileSize?: string
  /**
   * 名称
   */
  name?: string
  /**
   * 录像触发者ID(可选)
   */
  recorderId?: string
  /**
   * 保密属性(必选)缺省为0;0:不涉密,1:涉密
   */
  secrecy?: number
  /**
   * 录像开始时间(可选)
   */
  startTime?: string
  /**
   * 录像产生类型(可选)time或alarm 或 manua
   */
  type?: string
}

/**
 * DeviceVO
 */
export interface DeviceVO {
  /**
   * 是否激活
   */
  activated?: boolean
  /**
   * 激活人
   */
  activeBy?: string
  /**
   * 安装地址
   */
  address?: string
  /**
   * 区域名称
   */
  areaName?: string
  /**
   * 批次号
   */
  batchNum?: string
  /**
   * 蓝牙开关 true开 false关
   */
  bluetooth?: boolean
  /**
   * 安装经纬度
   */
  coordinate?: GeoPoint
  /**
   * 客户id
   */
  customerId?: number
  /**
   * 客户名称
   */
  customerName?: string
  /**
   * 对外设备型号
   */
  externalModel?: string
  heartbeatTime?: string
  /**
   * SIM卡号
   */
  iccd?: string
  /**
   * 设备id
   */
  id?: number
  /**
   * 机器ID
   */
  imei?: string
  /**
   * 对内设备型号
   */
  innerModel?: number
  /**
   * 对内设备型号名称
   */
  innerModelName?: string
  /**
   * 是否已安装
   */
  installed?: boolean
  /**
   * 安装图片
   */
  installImages?: FileInfo[]
  /**
   * 是否安装
   */
  installStatus?: boolean
  /**
   * 安装时间
   */
  installTime?: string
  /**
   * 安装人员姓名
   */
  installUserName?: string
  /**
   * 安装工单编号
   */
  installWorkOrderNo?: string
  /**
   * 联网功能
   */
  internetEnable?: boolean
  isAlarm?: boolean
  lastData?: Array<Array<{ [key: string]: any }[]>>
  /**
   * 杂项
   */
  misc?: any
  /**
   * 设备型号Id
   */
  modelId?: number
  modifyTime?: string
  /**
   * 在线
   */
  online?: boolean
  /**
   * 设备协议
   */
  protocol?: number
  /**
   * 设备协议名称
   */
  protocolName?: string
  /**
   * 报修状态
   */
  repairStatus?: boolean
  /**
   * 设备编码(系统内部编号)
   */
  serialNum?: string
  /**
   * 静态模式
   */
  staticMode?: boolean
  /**
   * 设备类型
   * 1 杀虫灯
   * 2 墒情
   * 3 虫情分析
   * 4 监控
   */
  type?: number
  /**
   * 设备版本
   */
  version?: string
  /**
   * 质保截至时间(订单创建时间+质保天数)
   */
  warrantyPeriodDate?: string
}

/**
 * 安装经纬度
 *
 * GeoPoint
 */
export interface GeoPoint {
  latitude?: number
  longitude?: number
}

/**
 * 文件对象
 *
 * FileInfo
 */
export interface FileInfo {
  /**
   * 文件名称
   */
  name?: string
  /**
   * 原始名称
   */
  originalFilename?: string
  /**
   * 大小
   */
  size?: number
  /**
   * 格式
   */
  type?: string
  /**
   * 单位
   */
  unit?: string
  /**
   * 文件URL
   */
  url?: string
}
